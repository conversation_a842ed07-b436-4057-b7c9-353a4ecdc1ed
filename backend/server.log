INFO:     Will watch for changes in these directories: ['/Users/<USER>/project/numerology/backend']
INFO:     <PERSON><PERSON><PERSON> running on http://0.0.0.0:8000 (Press CTRL+C to quit)
INFO:     Started reloader process [35518] using WatchFiles
INFO:     Started server process [35523]
INFO:     Waiting for application startup.
INFO:app.main:启动 Numerology API v1.0.0
INFO:app.main:调试模式: True
2025-07-12 19:21:57,101 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-12 19:21:57,101 INFO sqlalchemy.engine.Engine COMMIT
INFO:sqlalchemy.engine.Engine:COMMIT
INFO:app.main:数据库表初始化完成
INFO:     Application startup complete.
INFO:app.main:请求开始: GET http://localhost:8000/health
INFO:app.main:请求完成: 200 - 0.0009s
INFO:     127.0.0.1:54994 - "GET /health HTTP/1.1" 200 OK
INFO:app.main:请求开始: GET http://localhost:8000/docs
INFO:app.main:请求完成: 200 - 0.0005s
INFO:     127.0.0.1:55085 - "GET /docs HTTP/1.1" 200 OK
INFO:app.main:请求开始: GET http://127.0.0.1:8000/health
INFO:app.main:请求完成: 200 - 0.0027s
INFO:     127.0.0.1:55661 - "GET /health HTTP/1.1" 200 OK
INFO:app.main:请求开始: GET http://127.0.0.1:8000/
INFO:app.main:请求完成: 200 - 0.0004s
INFO:     127.0.0.1:55709 - "GET / HTTP/1.1" 200 OK
INFO:app.main:请求开始: GET http://localhost:8000/docs
INFO:app.main:请求完成: 200 - 0.0014s
INFO:     127.0.0.1:55763 - "GET /docs HTTP/1.1" 200 OK
INFO:app.main:请求开始: GET http://localhost:8000/openapi.json
INFO:app.main:请求完成: 200 - 0.0399s
INFO:     127.0.0.1:55763 - "GET /openapi.json HTTP/1.1" 200 OK
INFO:app.main:请求开始: GET http://localhost:8000/health
INFO:app.main:请求完成: 200 - 0.0038s
INFO:     127.0.0.1:57161 - "GET /health HTTP/1.1" 200 OK
INFO:app.main:请求开始: OPTIONS http://localhost:8000/api/auth/login
INFO:app.main:请求完成: 200 - 0.0014s
INFO:     127.0.0.1:57208 - "OPTIONS /api/auth/login HTTP/1.1" 200 OK
INFO:app.main:请求开始: POST http://localhost:8000/api/auth/login
2025-07-12 19:32:56,385 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-12 19:32:56,390 INFO sqlalchemy.engine.Engine SELECT users.id AS users_id, users.username AS users_username, users.phone AS users_phone, users.email AS users_email, users.password_hash AS users_password_hash, users.wechat_openid AS users_wechat_openid, users.wechat_unionid AS users_wechat_unionid, users.nickname AS users_nickname, users.avatar_url AS users_avatar_url, users.gender AS users_gender, users.birth_date AS users_birth_date, users.location AS users_location, users.is_vip AS users_is_vip, users.vip_expire_at AS users_vip_expire_at, users.vip_level AS users_vip_level, users.is_active AS users_is_active, users.is_verified AS users_is_verified, users.is_blocked AS users_is_blocked, users.block_reason AS users_block_reason, users.last_login_at AS users_last_login_at, users.last_login_ip AS users_last_login_ip, users.login_count AS users_login_count, users.created_at AS users_created_at, users.updated_at AS users_updated_at 
FROM users 
WHERE users.username = ? OR users.phone = ? OR users.email = ?
 LIMIT ? OFFSET ?
INFO:sqlalchemy.engine.Engine:SELECT users.id AS users_id, users.username AS users_username, users.phone AS users_phone, users.email AS users_email, users.password_hash AS users_password_hash, users.wechat_openid AS users_wechat_openid, users.wechat_unionid AS users_wechat_unionid, users.nickname AS users_nickname, users.avatar_url AS users_avatar_url, users.gender AS users_gender, users.birth_date AS users_birth_date, users.location AS users_location, users.is_vip AS users_is_vip, users.vip_expire_at AS users_vip_expire_at, users.vip_level AS users_vip_level, users.is_active AS users_is_active, users.is_verified AS users_is_verified, users.is_blocked AS users_is_blocked, users.block_reason AS users_block_reason, users.last_login_at AS users_last_login_at, users.last_login_ip AS users_last_login_ip, users.login_count AS users_login_count, users.created_at AS users_created_at, users.updated_at AS users_updated_at 
FROM users 
WHERE users.username = ? OR users.phone = ? OR users.email = ?
 LIMIT ? OFFSET ?
2025-07-12 19:32:56,390 INFO sqlalchemy.engine.Engine [generated in 0.00046s] ('testuser1', 'testuser1', 'testuser1', 1, 0)
INFO:sqlalchemy.engine.Engine:[generated in 0.00046s] ('testuser1', 'testuser1', 'testuser1', 1, 0)
WARNING:passlib.handlers.bcrypt:(trapped) error reading bcrypt version
Traceback (most recent call last):
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/passlib/handlers/bcrypt.py", line 620, in _load_backend_mixin
    version = _bcrypt.__about__.__version__
AttributeError: module 'bcrypt' has no attribute '__about__'
2025-07-12 19:32:56,616 INFO sqlalchemy.engine.Engine UPDATE users SET last_login_at=?, last_login_ip=?, login_count=?, updated_at=CURRENT_TIMESTAMP WHERE users.id = ?
INFO:sqlalchemy.engine.Engine:UPDATE users SET last_login_at=?, last_login_ip=?, login_count=?, updated_at=CURRENT_TIMESTAMP WHERE users.id = ?
2025-07-12 19:32:56,617 INFO sqlalchemy.engine.Engine [generated in 0.00011s] ('2025-07-12 11:32:56.615844', '127.0.0.1', 1, 2)
INFO:sqlalchemy.engine.Engine:[generated in 0.00011s] ('2025-07-12 11:32:56.615844', '127.0.0.1', 1, 2)
2025-07-12 19:32:56,617 INFO sqlalchemy.engine.Engine INSERT INTO user_tokens (user_id, token_type, token_hash, jti, expires_at, is_revoked, revoked_at, revoked_reason, device_id, user_agent, ip_address) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
INFO:sqlalchemy.engine.Engine:INSERT INTO user_tokens (user_id, token_type, token_hash, jti, expires_at, is_revoked, revoked_at, revoked_reason, device_id, user_agent, ip_address) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-12 19:32:56,617 INFO sqlalchemy.engine.Engine [generated in 0.00007s] (2, 'ACCESS', '1ca468a6621406868dd5163f7a54169e005542223dd0c1ec4e81d57722cd58c3', '7f0d9a96-4ea9-4118-9dc2-b58a2a78720a', '2025-07-12 12:02:56.615941', 0, None, None, None, 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '127.0.0.1')
INFO:sqlalchemy.engine.Engine:[generated in 0.00007s] (2, 'ACCESS', '1ca468a6621406868dd5163f7a54169e005542223dd0c1ec4e81d57722cd58c3', '7f0d9a96-4ea9-4118-9dc2-b58a2a78720a', '2025-07-12 12:02:56.615941', 0, None, None, None, 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '127.0.0.1')
2025-07-12 19:32:56,620 INFO sqlalchemy.engine.Engine INSERT INTO user_tokens (user_id, token_type, token_hash, jti, expires_at, is_revoked, revoked_at, revoked_reason, device_id, user_agent, ip_address) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
INFO:sqlalchemy.engine.Engine:INSERT INTO user_tokens (user_id, token_type, token_hash, jti, expires_at, is_revoked, revoked_at, revoked_reason, device_id, user_agent, ip_address) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-12 19:32:56,620 INFO sqlalchemy.engine.Engine [cached since 0.002412s ago] (2, 'REFRESH', 'd81de56df4db38737a655733debfc7d76c27204853164d66e8b418236f15496e', 'a6bd54e5-331e-4d20-b14d-697a5fc5b529', '2025-07-19 11:32:56.616082', 0, None, None, None, 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '127.0.0.1')
INFO:sqlalchemy.engine.Engine:[cached since 0.002412s ago] (2, 'REFRESH', 'd81de56df4db38737a655733debfc7d76c27204853164d66e8b418236f15496e', 'a6bd54e5-331e-4d20-b14d-697a5fc5b529', '2025-07-19 11:32:56.616082', 0, None, None, None, 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '127.0.0.1')
2025-07-12 19:32:56,620 INFO sqlalchemy.engine.Engine COMMIT
INFO:sqlalchemy.engine.Engine:COMMIT
2025-07-12 19:32:56,621 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-12 19:32:56,621 INFO sqlalchemy.engine.Engine SELECT users.id AS users_id, users.username AS users_username, users.phone AS users_phone, users.email AS users_email, users.password_hash AS users_password_hash, users.wechat_openid AS users_wechat_openid, users.wechat_unionid AS users_wechat_unionid, users.nickname AS users_nickname, users.avatar_url AS users_avatar_url, users.gender AS users_gender, users.birth_date AS users_birth_date, users.location AS users_location, users.is_vip AS users_is_vip, users.vip_expire_at AS users_vip_expire_at, users.vip_level AS users_vip_level, users.is_active AS users_is_active, users.is_verified AS users_is_verified, users.is_blocked AS users_is_blocked, users.block_reason AS users_block_reason, users.last_login_at AS users_last_login_at, users.last_login_ip AS users_last_login_ip, users.login_count AS users_login_count, users.created_at AS users_created_at, users.updated_at AS users_updated_at 
FROM users 
WHERE users.id = ?
INFO:sqlalchemy.engine.Engine:SELECT users.id AS users_id, users.username AS users_username, users.phone AS users_phone, users.email AS users_email, users.password_hash AS users_password_hash, users.wechat_openid AS users_wechat_openid, users.wechat_unionid AS users_wechat_unionid, users.nickname AS users_nickname, users.avatar_url AS users_avatar_url, users.gender AS users_gender, users.birth_date AS users_birth_date, users.location AS users_location, users.is_vip AS users_is_vip, users.vip_expire_at AS users_vip_expire_at, users.vip_level AS users_vip_level, users.is_active AS users_is_active, users.is_verified AS users_is_verified, users.is_blocked AS users_is_blocked, users.block_reason AS users_block_reason, users.last_login_at AS users_last_login_at, users.last_login_ip AS users_last_login_ip, users.login_count AS users_login_count, users.created_at AS users_created_at, users.updated_at AS users_updated_at 
FROM users 
WHERE users.id = ?
2025-07-12 19:32:56,621 INFO sqlalchemy.engine.Engine [generated in 0.00005s] (2,)
INFO:sqlalchemy.engine.Engine:[generated in 0.00005s] (2,)
2025-07-12 19:32:56,622 INFO sqlalchemy.engine.Engine INSERT INTO login_logs (user_id, login_type, login_status, failure_reason, ip_address, user_agent, device_id, location) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
INFO:sqlalchemy.engine.Engine:INSERT INTO login_logs (user_id, login_type, login_status, failure_reason, ip_address, user_agent, device_id, location) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
2025-07-12 19:32:56,622 INFO sqlalchemy.engine.Engine [generated in 0.00005s] (2, <LoginType.PASSWORD: 'password'>, 'SUCCESS', None, '127.0.0.1', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', None, None)
INFO:sqlalchemy.engine.Engine:[generated in 0.00005s] (2, <LoginType.PASSWORD: 'password'>, 'SUCCESS', None, '127.0.0.1', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', None, None)
2025-07-12 19:32:56,622 INFO sqlalchemy.engine.Engine COMMIT
INFO:sqlalchemy.engine.Engine:COMMIT
2025-07-12 19:32:56,622 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-12 19:32:56,622 INFO sqlalchemy.engine.Engine SELECT users.id AS users_id, users.username AS users_username, users.phone AS users_phone, users.email AS users_email, users.password_hash AS users_password_hash, users.wechat_openid AS users_wechat_openid, users.wechat_unionid AS users_wechat_unionid, users.nickname AS users_nickname, users.avatar_url AS users_avatar_url, users.gender AS users_gender, users.birth_date AS users_birth_date, users.location AS users_location, users.is_vip AS users_is_vip, users.vip_expire_at AS users_vip_expire_at, users.vip_level AS users_vip_level, users.is_active AS users_is_active, users.is_verified AS users_is_verified, users.is_blocked AS users_is_blocked, users.block_reason AS users_block_reason, users.last_login_at AS users_last_login_at, users.last_login_ip AS users_last_login_ip, users.login_count AS users_login_count, users.created_at AS users_created_at, users.updated_at AS users_updated_at 
FROM users 
WHERE users.id = ?
INFO:sqlalchemy.engine.Engine:SELECT users.id AS users_id, users.username AS users_username, users.phone AS users_phone, users.email AS users_email, users.password_hash AS users_password_hash, users.wechat_openid AS users_wechat_openid, users.wechat_unionid AS users_wechat_unionid, users.nickname AS users_nickname, users.avatar_url AS users_avatar_url, users.gender AS users_gender, users.birth_date AS users_birth_date, users.location AS users_location, users.is_vip AS users_is_vip, users.vip_expire_at AS users_vip_expire_at, users.vip_level AS users_vip_level, users.is_active AS users_is_active, users.is_verified AS users_is_verified, users.is_blocked AS users_is_blocked, users.block_reason AS users_block_reason, users.last_login_at AS users_last_login_at, users.last_login_ip AS users_last_login_ip, users.login_count AS users_login_count, users.created_at AS users_created_at, users.updated_at AS users_updated_at 
FROM users 
WHERE users.id = ?
2025-07-12 19:32:56,623 INFO sqlalchemy.engine.Engine [cached since 0.001512s ago] (2,)
INFO:sqlalchemy.engine.Engine:[cached since 0.001512s ago] (2,)
WARNING:app.services.user_service:用户登录失败: 2 validation errors for UserInfoSchema
gender
  value is not a valid enumeration member; permitted: 'male', 'female', 'unknown' (type=type_error.enum; enum_values=[<Gender.MALE: 'male'>, <Gender.FEMALE: 'female'>, <Gender.UNKNOWN: 'unknown'>])
vip_level
  value is not a valid enumeration member; permitted: 'normal', 'silver', 'gold', 'platinum' (type=type_error.enum; enum_values=[<VipLevel.NORMAL: 'normal'>, <VipLevel.SILVER: 'silver'>, <VipLevel.GOLD: 'gold'>, <VipLevel.PLATINUM: 'platinum'>])
WARNING:app.routers.user_router:用户登录失败: 2 validation errors for UserInfoSchema
gender
  value is not a valid enumeration member; permitted: 'male', 'female', 'unknown' (type=type_error.enum; enum_values=[<Gender.MALE: 'male'>, <Gender.FEMALE: 'female'>, <Gender.UNKNOWN: 'unknown'>])
vip_level
  value is not a valid enumeration member; permitted: 'normal', 'silver', 'gold', 'platinum' (type=type_error.enum; enum_values=[<VipLevel.NORMAL: 'normal'>, <VipLevel.SILVER: 'silver'>, <VipLevel.GOLD: 'gold'>, <VipLevel.PLATINUM: 'platinum'>])
2025-07-12 19:32:56,624 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
INFO:app.main:请求完成: 401 - 0.2576s
INFO:     127.0.0.1:57208 - "POST /api/auth/login HTTP/1.1" 401 Unauthorized
INFO:watchfiles.main:3 changes detected
INFO:app.main:请求开始: OPTIONS http://localhost:8000/api/horoscope/daily?zodiac_sign=%E9%87%91%E7%89%9B%E5%BA%A7
INFO:app.main:请求完成: 200 - 0.0008s
INFO:     127.0.0.1:57289 - "OPTIONS /api/horoscope/daily?zodiac_sign=%E9%87%91%E7%89%9B%E5%BA%A7 HTTP/1.1" 200 OK
INFO:app.main:请求开始: GET http://localhost:8000/api/horoscope/daily?zodiac_sign=%E9%87%91%E7%89%9B%E5%BA%A7
INFO:app.main:请求完成: 404 - 0.0006s
INFO:     127.0.0.1:57289 - "GET /api/horoscope/daily?zodiac_sign=%E9%87%91%E7%89%9B%E5%BA%A7 HTTP/1.1" 404 Not Found
INFO:app.main:请求开始: POST http://localhost:8000/api/auth/login
2025-07-12 19:34:12,392 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-12 19:34:12,393 INFO sqlalchemy.engine.Engine SELECT users.id AS users_id, users.username AS users_username, users.phone AS users_phone, users.email AS users_email, users.password_hash AS users_password_hash, users.wechat_openid AS users_wechat_openid, users.wechat_unionid AS users_wechat_unionid, users.nickname AS users_nickname, users.avatar_url AS users_avatar_url, users.gender AS users_gender, users.birth_date AS users_birth_date, users.location AS users_location, users.is_vip AS users_is_vip, users.vip_expire_at AS users_vip_expire_at, users.vip_level AS users_vip_level, users.is_active AS users_is_active, users.is_verified AS users_is_verified, users.is_blocked AS users_is_blocked, users.block_reason AS users_block_reason, users.last_login_at AS users_last_login_at, users.last_login_ip AS users_last_login_ip, users.login_count AS users_login_count, users.created_at AS users_created_at, users.updated_at AS users_updated_at 
FROM users 
WHERE users.username = ? OR users.phone = ? OR users.email = ?
 LIMIT ? OFFSET ?
INFO:sqlalchemy.engine.Engine:SELECT users.id AS users_id, users.username AS users_username, users.phone AS users_phone, users.email AS users_email, users.password_hash AS users_password_hash, users.wechat_openid AS users_wechat_openid, users.wechat_unionid AS users_wechat_unionid, users.nickname AS users_nickname, users.avatar_url AS users_avatar_url, users.gender AS users_gender, users.birth_date AS users_birth_date, users.location AS users_location, users.is_vip AS users_is_vip, users.vip_expire_at AS users_vip_expire_at, users.vip_level AS users_vip_level, users.is_active AS users_is_active, users.is_verified AS users_is_verified, users.is_blocked AS users_is_blocked, users.block_reason AS users_block_reason, users.last_login_at AS users_last_login_at, users.last_login_ip AS users_last_login_ip, users.login_count AS users_login_count, users.created_at AS users_created_at, users.updated_at AS users_updated_at 
FROM users 
WHERE users.username = ? OR users.phone = ? OR users.email = ?
 LIMIT ? OFFSET ?
2025-07-12 19:34:12,393 INFO sqlalchemy.engine.Engine [cached since 76s ago] ('testuser1', 'testuser1', 'testuser1', 1, 0)
INFO:sqlalchemy.engine.Engine:[cached since 76s ago] ('testuser1', 'testuser1', 'testuser1', 1, 0)
2025-07-12 19:34:12,591 INFO sqlalchemy.engine.Engine UPDATE users SET last_login_at=?, login_count=?, updated_at=CURRENT_TIMESTAMP WHERE users.id = ?
INFO:sqlalchemy.engine.Engine:UPDATE users SET last_login_at=?, login_count=?, updated_at=CURRENT_TIMESTAMP WHERE users.id = ?
2025-07-12 19:34:12,591 INFO sqlalchemy.engine.Engine [generated in 0.00009s] ('2025-07-12 11:34:12.591286', 2, 2)
INFO:sqlalchemy.engine.Engine:[generated in 0.00009s] ('2025-07-12 11:34:12.591286', 2, 2)
2025-07-12 19:34:12,593 INFO sqlalchemy.engine.Engine INSERT INTO user_tokens (user_id, token_type, token_hash, jti, expires_at, is_revoked, revoked_at, revoked_reason, device_id, user_agent, ip_address) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
INFO:sqlalchemy.engine.Engine:INSERT INTO user_tokens (user_id, token_type, token_hash, jti, expires_at, is_revoked, revoked_at, revoked_reason, device_id, user_agent, ip_address) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-12 19:34:12,593 INFO sqlalchemy.engine.Engine [cached since 75.97s ago] (2, 'ACCESS', 'e058eb0b9c5b656e2df19a7d6336938568b4ddc337c1e7b26ac34f078b9397cd', '069a15d8-7f43-43ce-862e-7954a122234e', '2025-07-12 12:04:12.591344', 0, None, None, None, 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '127.0.0.1')
INFO:sqlalchemy.engine.Engine:[cached since 75.97s ago] (2, 'ACCESS', 'e058eb0b9c5b656e2df19a7d6336938568b4ddc337c1e7b26ac34f078b9397cd', '069a15d8-7f43-43ce-862e-7954a122234e', '2025-07-12 12:04:12.591344', 0, None, None, None, 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '127.0.0.1')
2025-07-12 19:34:12,594 INFO sqlalchemy.engine.Engine INSERT INTO user_tokens (user_id, token_type, token_hash, jti, expires_at, is_revoked, revoked_at, revoked_reason, device_id, user_agent, ip_address) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
INFO:sqlalchemy.engine.Engine:INSERT INTO user_tokens (user_id, token_type, token_hash, jti, expires_at, is_revoked, revoked_at, revoked_reason, device_id, user_agent, ip_address) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-12 19:34:12,594 INFO sqlalchemy.engine.Engine [cached since 75.98s ago] (2, 'REFRESH', '81f14f318ed2d66f37b0c0a809c914a91ad5b4a4b536f60a76f907564f5cca5f', '078ed716-524f-4d43-bc6a-9a83fb828183', '2025-07-19 11:34:12.591435', 0, None, None, None, 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '127.0.0.1')
INFO:sqlalchemy.engine.Engine:[cached since 75.98s ago] (2, 'REFRESH', '81f14f318ed2d66f37b0c0a809c914a91ad5b4a4b536f60a76f907564f5cca5f', '078ed716-524f-4d43-bc6a-9a83fb828183', '2025-07-19 11:34:12.591435', 0, None, None, None, 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '127.0.0.1')
2025-07-12 19:34:12,594 INFO sqlalchemy.engine.Engine COMMIT
INFO:sqlalchemy.engine.Engine:COMMIT
2025-07-12 19:34:12,595 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-12 19:34:12,595 INFO sqlalchemy.engine.Engine SELECT users.id AS users_id, users.username AS users_username, users.phone AS users_phone, users.email AS users_email, users.password_hash AS users_password_hash, users.wechat_openid AS users_wechat_openid, users.wechat_unionid AS users_wechat_unionid, users.nickname AS users_nickname, users.avatar_url AS users_avatar_url, users.gender AS users_gender, users.birth_date AS users_birth_date, users.location AS users_location, users.is_vip AS users_is_vip, users.vip_expire_at AS users_vip_expire_at, users.vip_level AS users_vip_level, users.is_active AS users_is_active, users.is_verified AS users_is_verified, users.is_blocked AS users_is_blocked, users.block_reason AS users_block_reason, users.last_login_at AS users_last_login_at, users.last_login_ip AS users_last_login_ip, users.login_count AS users_login_count, users.created_at AS users_created_at, users.updated_at AS users_updated_at 
FROM users 
WHERE users.id = ?
INFO:sqlalchemy.engine.Engine:SELECT users.id AS users_id, users.username AS users_username, users.phone AS users_phone, users.email AS users_email, users.password_hash AS users_password_hash, users.wechat_openid AS users_wechat_openid, users.wechat_unionid AS users_wechat_unionid, users.nickname AS users_nickname, users.avatar_url AS users_avatar_url, users.gender AS users_gender, users.birth_date AS users_birth_date, users.location AS users_location, users.is_vip AS users_is_vip, users.vip_expire_at AS users_vip_expire_at, users.vip_level AS users_vip_level, users.is_active AS users_is_active, users.is_verified AS users_is_verified, users.is_blocked AS users_is_blocked, users.block_reason AS users_block_reason, users.last_login_at AS users_last_login_at, users.last_login_ip AS users_last_login_ip, users.login_count AS users_login_count, users.created_at AS users_created_at, users.updated_at AS users_updated_at 
FROM users 
WHERE users.id = ?
2025-07-12 19:34:12,595 INFO sqlalchemy.engine.Engine [cached since 75.97s ago] (2,)
INFO:sqlalchemy.engine.Engine:[cached since 75.97s ago] (2,)
2025-07-12 19:34:12,595 INFO sqlalchemy.engine.Engine INSERT INTO login_logs (user_id, login_type, login_status, failure_reason, ip_address, user_agent, device_id, location) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
INFO:sqlalchemy.engine.Engine:INSERT INTO login_logs (user_id, login_type, login_status, failure_reason, ip_address, user_agent, device_id, location) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
2025-07-12 19:34:12,595 INFO sqlalchemy.engine.Engine [cached since 75.97s ago] (2, <LoginType.PASSWORD: 'password'>, 'SUCCESS', None, '127.0.0.1', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', None, None)
INFO:sqlalchemy.engine.Engine:[cached since 75.97s ago] (2, <LoginType.PASSWORD: 'password'>, 'SUCCESS', None, '127.0.0.1', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', None, None)
2025-07-12 19:34:12,596 INFO sqlalchemy.engine.Engine COMMIT
INFO:sqlalchemy.engine.Engine:COMMIT
2025-07-12 19:34:12,596 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-12 19:34:12,596 INFO sqlalchemy.engine.Engine SELECT users.id AS users_id, users.username AS users_username, users.phone AS users_phone, users.email AS users_email, users.password_hash AS users_password_hash, users.wechat_openid AS users_wechat_openid, users.wechat_unionid AS users_wechat_unionid, users.nickname AS users_nickname, users.avatar_url AS users_avatar_url, users.gender AS users_gender, users.birth_date AS users_birth_date, users.location AS users_location, users.is_vip AS users_is_vip, users.vip_expire_at AS users_vip_expire_at, users.vip_level AS users_vip_level, users.is_active AS users_is_active, users.is_verified AS users_is_verified, users.is_blocked AS users_is_blocked, users.block_reason AS users_block_reason, users.last_login_at AS users_last_login_at, users.last_login_ip AS users_last_login_ip, users.login_count AS users_login_count, users.created_at AS users_created_at, users.updated_at AS users_updated_at 
FROM users 
WHERE users.id = ?
INFO:sqlalchemy.engine.Engine:SELECT users.id AS users_id, users.username AS users_username, users.phone AS users_phone, users.email AS users_email, users.password_hash AS users_password_hash, users.wechat_openid AS users_wechat_openid, users.wechat_unionid AS users_wechat_unionid, users.nickname AS users_nickname, users.avatar_url AS users_avatar_url, users.gender AS users_gender, users.birth_date AS users_birth_date, users.location AS users_location, users.is_vip AS users_is_vip, users.vip_expire_at AS users_vip_expire_at, users.vip_level AS users_vip_level, users.is_active AS users_is_active, users.is_verified AS users_is_verified, users.is_blocked AS users_is_blocked, users.block_reason AS users_block_reason, users.last_login_at AS users_last_login_at, users.last_login_ip AS users_last_login_ip, users.login_count AS users_login_count, users.created_at AS users_created_at, users.updated_at AS users_updated_at 
FROM users 
WHERE users.id = ?
2025-07-12 19:34:12,596 INFO sqlalchemy.engine.Engine [cached since 75.97s ago] (2,)
INFO:sqlalchemy.engine.Engine:[cached since 75.97s ago] (2,)
WARNING:app.services.user_service:用户登录失败: 2 validation errors for UserInfoSchema
gender
  value is not a valid enumeration member; permitted: 'male', 'female', 'unknown' (type=type_error.enum; enum_values=[<Gender.MALE: 'male'>, <Gender.FEMALE: 'female'>, <Gender.UNKNOWN: 'unknown'>])
vip_level
  value is not a valid enumeration member; permitted: 'normal', 'silver', 'gold', 'platinum' (type=type_error.enum; enum_values=[<VipLevel.NORMAL: 'normal'>, <VipLevel.SILVER: 'silver'>, <VipLevel.GOLD: 'gold'>, <VipLevel.PLATINUM: 'platinum'>])
WARNING:app.routers.user_router:用户登录失败: 2 validation errors for UserInfoSchema
gender
  value is not a valid enumeration member; permitted: 'male', 'female', 'unknown' (type=type_error.enum; enum_values=[<Gender.MALE: 'male'>, <Gender.FEMALE: 'female'>, <Gender.UNKNOWN: 'unknown'>])
vip_level
  value is not a valid enumeration member; permitted: 'normal', 'silver', 'gold', 'platinum' (type=type_error.enum; enum_values=[<VipLevel.NORMAL: 'normal'>, <VipLevel.SILVER: 'silver'>, <VipLevel.GOLD: 'gold'>, <VipLevel.PLATINUM: 'platinum'>])
2025-07-12 19:34:12,597 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
INFO:app.main:请求完成: 401 - 0.2103s
INFO:     127.0.0.1:57425 - "POST /api/auth/login HTTP/1.1" 401 Unauthorized
INFO:watchfiles.main:3 changes detected
INFO:app.main:请求开始: GET http://localhost:8000/
INFO:app.main:请求完成: 200 - 0.0013s
INFO:     127.0.0.1:57613 - "GET / HTTP/1.1" 200 OK
INFO:app.main:请求开始: GET http://localhost:8000/
INFO:app.main:请求完成: 200 - 0.0006s
INFO:     127.0.0.1:57613 - "GET / HTTP/1.1" 200 OK
INFO:app.main:请求开始: GET http://localhost:8000/docs
INFO:app.main:请求完成: 200 - 0.0009s
INFO:     127.0.0.1:57621 - "GET /docs HTTP/1.1" 200 OK
INFO:app.main:请求开始: GET http://localhost:8000/openapi.json
INFO:app.main:请求完成: 200 - 0.0016s
INFO:     127.0.0.1:57621 - "GET /openapi.json HTTP/1.1" 200 OK
INFO:app.main:请求开始: OPTIONS http://localhost:8000/api/horoscope/weekly?zodiac_sign=%E9%87%91%E7%89%9B%E5%BA%A7
INFO:app.main:请求完成: 200 - 0.0033s
INFO:     127.0.0.1:57911 - "OPTIONS /api/horoscope/weekly?zodiac_sign=%E9%87%91%E7%89%9B%E5%BA%A7 HTTP/1.1" 200 OK
INFO:app.main:请求开始: GET http://localhost:8000/api/horoscope/weekly?zodiac_sign=%E9%87%91%E7%89%9B%E5%BA%A7
INFO:app.main:请求完成: 404 - 0.0011s
INFO:     127.0.0.1:57911 - "GET /api/horoscope/weekly?zodiac_sign=%E9%87%91%E7%89%9B%E5%BA%A7 HTTP/1.1" 404 Not Found
INFO:app.main:请求开始: OPTIONS http://localhost:8000/api/horoscope/monthly?zodiac_sign=%E9%87%91%E7%89%9B%E5%BA%A7
INFO:app.main:请求完成: 200 - 0.0009s
INFO:     127.0.0.1:57917 - "OPTIONS /api/horoscope/monthly?zodiac_sign=%E9%87%91%E7%89%9B%E5%BA%A7 HTTP/1.1" 200 OK
INFO:app.main:请求开始: GET http://localhost:8000/api/horoscope/monthly?zodiac_sign=%E9%87%91%E7%89%9B%E5%BA%A7
INFO:app.main:请求完成: 404 - 0.0007s
INFO:     127.0.0.1:57917 - "GET /api/horoscope/monthly?zodiac_sign=%E9%87%91%E7%89%9B%E5%BA%A7 HTTP/1.1" 404 Not Found
INFO:app.main:请求开始: GET http://localhost:8000/api/horoscope/weekly?zodiac_sign=%E9%87%91%E7%89%9B%E5%BA%A7
INFO:app.main:请求完成: 404 - 0.0012s
INFO:     127.0.0.1:57917 - "GET /api/horoscope/weekly?zodiac_sign=%E9%87%91%E7%89%9B%E5%BA%A7 HTTP/1.1" 404 Not Found
INFO:app.main:请求开始: GET http://localhost:8000/api/horoscope/daily?zodiac_sign=%E9%87%91%E7%89%9B%E5%BA%A7
INFO:app.main:请求完成: 404 - 0.0011s
INFO:     127.0.0.1:57917 - "GET /api/horoscope/daily?zodiac_sign=%E9%87%91%E7%89%9B%E5%BA%A7 HTTP/1.1" 404 Not Found
INFO:app.main:请求开始: OPTIONS http://localhost:8000/api/astro/calendar
INFO:app.main:请求完成: 200 - 0.0007s
INFO:     127.0.0.1:57917 - "OPTIONS /api/astro/calendar HTTP/1.1" 200 OK
INFO:app.main:请求开始: GET http://localhost:8000/api/astro/calendar
INFO:app.main:请求完成: 404 - 0.0007s
INFO:     127.0.0.1:57919 - "GET /api/astro/calendar HTTP/1.1" 404 Not Found
INFO:app.main:请求开始: OPTIONS http://localhost:8000/api/astro/upcoming-events?days=7
INFO:app.main:请求完成: 200 - 0.0007s
INFO:     127.0.0.1:57919 - "OPTIONS /api/astro/upcoming-events?days=7 HTTP/1.1" 200 OK
INFO:app.main:请求开始: GET http://localhost:8000/api/astro/upcoming-events?days=7
INFO:app.main:请求完成: 404 - 0.0007s
INFO:     127.0.0.1:57917 - "GET /api/astro/upcoming-events?days=7 HTTP/1.1" 404 Not Found
INFO:app.main:请求开始: OPTIONS http://localhost:8000/api/psychology/test/enneagram
INFO:app.main:请求完成: 200 - 0.0008s
INFO:     127.0.0.1:57928 - "OPTIONS /api/psychology/test/enneagram HTTP/1.1" 200 OK
INFO:app.main:请求开始: GET http://localhost:8000/api/psychology/test/enneagram
INFO:app.main:请求完成: 404 - 0.0006s
INFO:     127.0.0.1:57928 - "GET /api/psychology/test/enneagram HTTP/1.1" 404 Not Found
INFO:app.main:请求开始: OPTIONS http://localhost:8000/api/psychology/test/big-five
INFO:app.main:请求完成: 200 - 0.0007s
INFO:     127.0.0.1:57928 - "OPTIONS /api/psychology/test/big-five HTTP/1.1" 200 OK
INFO:app.main:请求开始: GET http://localhost:8000/api/psychology/test/big-five
INFO:app.main:请求完成: 404 - 0.0007s
INFO:     127.0.0.1:57930 - "GET /api/psychology/test/big-five HTTP/1.1" 404 Not Found
INFO:app.main:请求开始: GET http://localhost:8000/api/psychology/test/big-five
INFO:app.main:请求完成: 404 - 0.0010s
INFO:     127.0.0.1:57930 - "GET /api/psychology/test/big-five HTTP/1.1" 404 Not Found
INFO:app.main:请求开始: OPTIONS http://localhost:8000/api/psychology/test/mbti
INFO:app.main:请求完成: 200 - 0.0007s
INFO:     127.0.0.1:57930 - "OPTIONS /api/psychology/test/mbti HTTP/1.1" 200 OK
INFO:app.main:请求开始: GET http://localhost:8000/api/psychology/test/mbti
INFO:app.main:请求完成: 404 - 0.0045s
INFO:     127.0.0.1:57928 - "GET /api/psychology/test/mbti HTTP/1.1" 404 Not Found
INFO:app.main:请求开始: GET http://localhost:8000/api/psychology/test/enneagram
INFO:app.main:请求完成: 404 - 0.0011s
INFO:     127.0.0.1:57939 - "GET /api/psychology/test/enneagram HTTP/1.1" 404 Not Found
INFO:app.main:请求开始: GET http://localhost:8000/api/psychology/test/big-five
INFO:app.main:请求完成: 404 - 0.0015s
INFO:     127.0.0.1:57939 - "GET /api/psychology/test/big-five HTTP/1.1" 404 Not Found
INFO:app.main:请求开始: GET http://localhost:8000/health
INFO:app.main:请求完成: 200 - 0.0020s
INFO:     127.0.0.1:58146 - "GET /health HTTP/1.1" 200 OK
INFO:app.main:请求开始: GET http://localhost:8000/health
INFO:app.main:请求完成: 200 - 0.0010s
INFO:     127.0.0.1:58172 - "GET /health HTTP/1.1" 200 OK
INFO:app.main:请求开始: OPTIONS http://localhost:8000/api/meditation/recommendations?mood=stressed&time=10
INFO:app.main:请求完成: 200 - 0.0014s
INFO:     127.0.0.1:58540 - "OPTIONS /api/meditation/recommendations?mood=stressed&time=10 HTTP/1.1" 200 OK
INFO:app.main:请求开始: GET http://localhost:8000/api/meditation/recommendations?mood=stressed&time=10
INFO:app.main:请求完成: 404 - 0.0006s
INFO:     127.0.0.1:58540 - "GET /api/meditation/recommendations?mood=stressed&time=10 HTTP/1.1" 404 Not Found
INFO:app.main:请求开始: OPTIONS http://localhost:8000/api/breathing/exercises?goal=%E6%94%BE%E6%9D%BE
INFO:app.main:请求完成: 200 - 0.0006s
INFO:     127.0.0.1:58600 - "OPTIONS /api/breathing/exercises?goal=%E6%94%BE%E6%9D%BE HTTP/1.1" 200 OK
INFO:app.main:请求开始: GET http://localhost:8000/api/breathing/exercises?goal=%E6%94%BE%E6%9D%BE
INFO:app.main:请求完成: 404 - 0.0007s
INFO:     127.0.0.1:58600 - "GET /api/breathing/exercises?goal=%E6%94%BE%E6%9D%BE HTTP/1.1" 404 Not Found
INFO:app.main:请求开始: GET http://localhost:8000/api/breathing/exercises?goal=%E6%94%BE%E6%9D%BE
INFO:app.main:请求完成: 404 - 0.0005s
INFO:     127.0.0.1:58600 - "GET /api/breathing/exercises?goal=%E6%94%BE%E6%9D%BE HTTP/1.1" 404 Not Found
INFO:app.main:请求开始: GET http://localhost:8000/api/breathing/exercises?goal=%E6%94%BE%E6%9D%BE
INFO:app.main:请求完成: 404 - 0.0014s
INFO:     127.0.0.1:58642 - "GET /api/breathing/exercises?goal=%E6%94%BE%E6%9D%BE HTTP/1.1" 404 Not Found
INFO:app.main:请求开始: OPTIONS http://localhost:8000/api/mood/analysis
INFO:app.main:请求完成: 200 - 0.0007s
INFO:     127.0.0.1:58642 - "OPTIONS /api/mood/analysis HTTP/1.1" 200 OK
INFO:app.main:请求开始: GET http://localhost:8000/api/mood/analysis
INFO:app.main:请求完成: 404 - 0.0007s
INFO:     127.0.0.1:58666 - "GET /api/mood/analysis HTTP/1.1" 404 Not Found
INFO:app.main:请求开始: GET http://localhost:8000/health
INFO:app.main:请求完成: 200 - 0.0046s
INFO:     127.0.0.1:63300 - "GET /health HTTP/1.1" 200 OK
INFO:watchfiles.main:3 changes detected
WARNING:  WatchFiles detected changes in 'app/routers/daily_mood_router.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:app.main:应用正在关闭...
INFO:     Application shutdown complete.
INFO:     Finished server process [35523]
INFO:     Started server process [38785]
INFO:     Waiting for application startup.
INFO:app.main:启动 Numerology API v1.0.0
INFO:app.main:调试模式: True
2025-07-13 15:48:27,798 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-13 15:48:27,799 INFO sqlalchemy.engine.Engine COMMIT
INFO:sqlalchemy.engine.Engine:COMMIT
INFO:app.main:数据库表初始化完成
INFO:     Application startup complete.
INFO:watchfiles.main:3 changes detected
WARNING:  WatchFiles detected changes in 'app/routers/emotion_diary_router.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:app.main:应用正在关闭...
INFO:     Application shutdown complete.
INFO:     Finished server process [38785]
INFO:     Started server process [38923]
INFO:     Waiting for application startup.
INFO:app.main:启动 Numerology API v1.0.0
INFO:app.main:调试模式: True
2025-07-13 15:48:57,684 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-13 15:48:57,685 INFO sqlalchemy.engine.Engine COMMIT
INFO:sqlalchemy.engine.Engine:COMMIT
INFO:app.main:数据库表初始化完成
INFO:     Application startup complete.
INFO:watchfiles.main:3 changes detected
WARNING:  WatchFiles detected changes in 'app/models/daily_mood.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:app.main:应用正在关闭...
INFO:     Application shutdown complete.
INFO:     Finished server process [38923]
INFO:     Started server process [39040]
INFO:     Waiting for application startup.
INFO:app.main:启动 Numerology API v1.0.0
INFO:app.main:调试模式: True
2025-07-13 15:49:23,617 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-13 15:49:23,617 INFO sqlalchemy.engine.Engine COMMIT
INFO:sqlalchemy.engine.Engine:COMMIT
INFO:app.main:数据库表初始化完成
INFO:     Application startup complete.
INFO:watchfiles.main:3 changes detected
WARNING:  WatchFiles detected changes in 'app/schemas/daily_mood.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:app.main:应用正在关闭...
INFO:     Application shutdown complete.
INFO:     Finished server process [39040]
INFO:     Started server process [39202]
INFO:     Waiting for application startup.
INFO:app.main:启动 Numerology API v1.0.0
INFO:app.main:调试模式: True
2025-07-13 15:49:53,638 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-13 15:49:53,638 INFO sqlalchemy.engine.Engine COMMIT
INFO:sqlalchemy.engine.Engine:COMMIT
INFO:app.main:数据库表初始化完成
INFO:     Application startup complete.
INFO:watchfiles.main:3 changes detected
WARNING:  WatchFiles detected changes in 'app/schemas/emotion_diary.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:app.main:应用正在关闭...
INFO:     Application shutdown complete.
INFO:     Finished server process [39202]
INFO:     Started server process [39254]
INFO:     Waiting for application startup.
INFO:app.main:启动 Numerology API v1.0.0
INFO:app.main:调试模式: True
2025-07-13 15:50:05,181 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-13 15:50:05,181 INFO sqlalchemy.engine.Engine COMMIT
INFO:sqlalchemy.engine.Engine:COMMIT
INFO:app.main:数据库表初始化完成
INFO:     Application startup complete.
INFO:watchfiles.main:3 changes detected
WARNING:  WatchFiles detected changes in 'app/services/emotion_diary_service.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:app.main:应用正在关闭...
INFO:     Application shutdown complete.
INFO:     Finished server process [39254]
INFO:     Started server process [39517]
INFO:     Waiting for application startup.
INFO:app.main:启动 Numerology API v1.0.0
INFO:app.main:调试模式: True
2025-07-13 15:50:50,767 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-13 15:50:50,767 INFO sqlalchemy.engine.Engine COMMIT
INFO:sqlalchemy.engine.Engine:COMMIT
INFO:app.main:数据库表初始化完成
INFO:     Application startup complete.
INFO:watchfiles.main:3 changes detected
WARNING:  WatchFiles detected changes in 'app/main.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:app.main:应用正在关闭...
INFO:     Application shutdown complete.
INFO:     Finished server process [39517]
Traceback (most recent call last):
  File "<string>", line 1, in <module>
  File "/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/spawn.py", line 116, in spawn_main
    exitcode = _main(fd, parent_sentinel)
  File "/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/spawn.py", line 125, in _main
    prepare(preparation_data)
  File "/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/spawn.py", line 236, in prepare
    _fixup_main_from_path(data['init_main_from_path'])
  File "/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/spawn.py", line 287, in _fixup_main_from_path
    main_content = runpy.run_path(main_path,
  File "/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/runpy.py", line 268, in run_path
    return _run_module_code(code, init_globals, run_name,
  File "/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/runpy.py", line 97, in _run_module_code
    _run_code(code, mod_globals, init_globals,
  File "/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/runpy.py", line 87, in _run_code
    exec(code, run_globals)
  File "/Users/<USER>/project/numerology/backend/run.py", line 8, in <module>
    from app.main import app
  File "/Users/<USER>/project/numerology/backend/app/main.py", line 12, in <module>
    from app.routers import horoscope_router, user_router, analysis_router, daily_mood_router, emotion_diary_router
  File "/Users/<USER>/project/numerology/backend/app/routers/daily_mood_router.py", line 14, in <module>
    from app.services.daily_mood_service import DailyMoodService
  File "/Users/<USER>/project/numerology/backend/app/services/daily_mood_service.py", line 8, in <module>
    from app.services.enhanced_horoscope_service import enhanced_horoscope_service
  File "/Users/<USER>/project/numerology/backend/app/services/enhanced_horoscope_service.py", line 507, in <module>
    enhanced_horoscope_service = EnhancedHoroscopeService()
  File "/Users/<USER>/project/numerology/backend/app/services/enhanced_horoscope_service.py", line 124, in __init__
    self.cache_service = HoroscopeCacheService()
TypeError: __init__() missing 1 required positional argument: 'redis_client'
INFO:watchfiles.main:3 changes detected
WARNING:  WatchFiles detected changes in 'app/main.py'. Reloading...
Traceback (most recent call last):
  File "<string>", line 1, in <module>
  File "/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/spawn.py", line 116, in spawn_main
    exitcode = _main(fd, parent_sentinel)
  File "/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/spawn.py", line 125, in _main
    prepare(preparation_data)
  File "/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/spawn.py", line 236, in prepare
    _fixup_main_from_path(data['init_main_from_path'])
  File "/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/spawn.py", line 287, in _fixup_main_from_path
    main_content = runpy.run_path(main_path,
  File "/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/runpy.py", line 268, in run_path
    return _run_module_code(code, init_globals, run_name,
  File "/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/runpy.py", line 97, in _run_module_code
    _run_code(code, mod_globals, init_globals,
  File "/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/runpy.py", line 87, in _run_code
    exec(code, run_globals)
  File "/Users/<USER>/project/numerology/backend/run.py", line 8, in <module>
    from app.main import app
  File "/Users/<USER>/project/numerology/backend/app/main.py", line 12, in <module>
    from app.routers import horoscope_router, user_router, analysis_router, daily_mood_router, emotion_diary_router
  File "/Users/<USER>/project/numerology/backend/app/routers/daily_mood_router.py", line 14, in <module>
    from app.services.daily_mood_service import DailyMoodService
  File "/Users/<USER>/project/numerology/backend/app/services/daily_mood_service.py", line 8, in <module>
    from app.services.enhanced_horoscope_service import enhanced_horoscope_service
  File "/Users/<USER>/project/numerology/backend/app/services/enhanced_horoscope_service.py", line 507, in <module>
    enhanced_horoscope_service = EnhancedHoroscopeService()
  File "/Users/<USER>/project/numerology/backend/app/services/enhanced_horoscope_service.py", line 124, in __init__
    self.cache_service = HoroscopeCacheService()
TypeError: __init__() missing 1 required positional argument: 'redis_client'
INFO:watchfiles.main:3 changes detected
WARNING:  WatchFiles detected changes in 'app/models/user.py'. Reloading...
Traceback (most recent call last):
  File "<string>", line 1, in <module>
  File "/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/spawn.py", line 116, in spawn_main
    exitcode = _main(fd, parent_sentinel)
  File "/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/spawn.py", line 125, in _main
    prepare(preparation_data)
  File "/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/spawn.py", line 236, in prepare
    _fixup_main_from_path(data['init_main_from_path'])
  File "/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/spawn.py", line 287, in _fixup_main_from_path
    main_content = runpy.run_path(main_path,
  File "/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/runpy.py", line 268, in run_path
    return _run_module_code(code, init_globals, run_name,
  File "/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/runpy.py", line 97, in _run_module_code
    _run_code(code, mod_globals, init_globals,
  File "/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/runpy.py", line 87, in _run_code
    exec(code, run_globals)
  File "/Users/<USER>/project/numerology/backend/run.py", line 8, in <module>
    from app.main import app
  File "/Users/<USER>/project/numerology/backend/app/main.py", line 12, in <module>
    from app.routers import horoscope_router, user_router, analysis_router, daily_mood_router, emotion_diary_router
  File "/Users/<USER>/project/numerology/backend/app/routers/daily_mood_router.py", line 14, in <module>
    from app.services.daily_mood_service import DailyMoodService
  File "/Users/<USER>/project/numerology/backend/app/services/daily_mood_service.py", line 8, in <module>
    from app.services.enhanced_horoscope_service import enhanced_horoscope_service
  File "/Users/<USER>/project/numerology/backend/app/services/enhanced_horoscope_service.py", line 507, in <module>
    enhanced_horoscope_service = EnhancedHoroscopeService()
  File "/Users/<USER>/project/numerology/backend/app/services/enhanced_horoscope_service.py", line 124, in __init__
    self.cache_service = HoroscopeCacheService()
TypeError: __init__() missing 1 required positional argument: 'redis_client'
INFO:watchfiles.main:3 changes detected
WARNING:  WatchFiles detected changes in 'app/models/user.py'. Reloading...
Traceback (most recent call last):
  File "<string>", line 1, in <module>
  File "/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/spawn.py", line 116, in spawn_main
    exitcode = _main(fd, parent_sentinel)
  File "/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/spawn.py", line 125, in _main
    prepare(preparation_data)
  File "/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/spawn.py", line 236, in prepare
    _fixup_main_from_path(data['init_main_from_path'])
  File "/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/spawn.py", line 287, in _fixup_main_from_path
    main_content = runpy.run_path(main_path,
  File "/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/runpy.py", line 268, in run_path
    return _run_module_code(code, init_globals, run_name,
  File "/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/runpy.py", line 97, in _run_module_code
    _run_code(code, mod_globals, init_globals,
  File "/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/runpy.py", line 87, in _run_code
    exec(code, run_globals)
  File "/Users/<USER>/project/numerology/backend/run.py", line 8, in <module>
    from app.main import app
  File "/Users/<USER>/project/numerology/backend/app/main.py", line 12, in <module>
    from app.routers import horoscope_router, user_router, analysis_router, daily_mood_router, emotion_diary_router
  File "/Users/<USER>/project/numerology/backend/app/routers/daily_mood_router.py", line 14, in <module>
    from app.services.daily_mood_service import DailyMoodService
  File "/Users/<USER>/project/numerology/backend/app/services/daily_mood_service.py", line 8, in <module>
    from app.services.enhanced_horoscope_service import enhanced_horoscope_service
  File "/Users/<USER>/project/numerology/backend/app/services/enhanced_horoscope_service.py", line 507, in <module>
    enhanced_horoscope_service = EnhancedHoroscopeService()
  File "/Users/<USER>/project/numerology/backend/app/services/enhanced_horoscope_service.py", line 124, in __init__
    self.cache_service = HoroscopeCacheService()
TypeError: __init__() missing 1 required positional argument: 'redis_client'
INFO:watchfiles.main:3 changes detected
WARNING:  WatchFiles detected changes in 'app/core/config.py'. Reloading...
Traceback (most recent call last):
  File "<string>", line 1, in <module>
  File "/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/spawn.py", line 116, in spawn_main
    exitcode = _main(fd, parent_sentinel)
  File "/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/spawn.py", line 125, in _main
    prepare(preparation_data)
  File "/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/spawn.py", line 236, in prepare
    _fixup_main_from_path(data['init_main_from_path'])
  File "/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/spawn.py", line 287, in _fixup_main_from_path
    main_content = runpy.run_path(main_path,
  File "/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/runpy.py", line 268, in run_path
    return _run_module_code(code, init_globals, run_name,
  File "/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/runpy.py", line 97, in _run_module_code
    _run_code(code, mod_globals, init_globals,
  File "/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/runpy.py", line 87, in _run_code
    exec(code, run_globals)
  File "/Users/<USER>/project/numerology/backend/run.py", line 8, in <module>
    from app.main import app
  File "/Users/<USER>/project/numerology/backend/app/main.py", line 12, in <module>
    from app.routers import horoscope_router, user_router, analysis_router, daily_mood_router, emotion_diary_router
  File "/Users/<USER>/project/numerology/backend/app/routers/daily_mood_router.py", line 14, in <module>
    from app.services.daily_mood_service import DailyMoodService
  File "/Users/<USER>/project/numerology/backend/app/services/daily_mood_service.py", line 8, in <module>
    from app.services.enhanced_horoscope_service import enhanced_horoscope_service
  File "/Users/<USER>/project/numerology/backend/app/services/enhanced_horoscope_service.py", line 507, in <module>
    enhanced_horoscope_service = EnhancedHoroscopeService()
  File "/Users/<USER>/project/numerology/backend/app/services/enhanced_horoscope_service.py", line 124, in __init__
    self.cache_service = HoroscopeCacheService()
TypeError: __init__() missing 1 required positional argument: 'redis_client'
INFO:watchfiles.main:3 changes detected
WARNING:  WatchFiles detected changes in 'app/core/config.py'. Reloading...
Traceback (most recent call last):
  File "<string>", line 1, in <module>
  File "/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/spawn.py", line 116, in spawn_main
    exitcode = _main(fd, parent_sentinel)
  File "/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/spawn.py", line 125, in _main
    prepare(preparation_data)
  File "/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/spawn.py", line 236, in prepare
    _fixup_main_from_path(data['init_main_from_path'])
  File "/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/spawn.py", line 287, in _fixup_main_from_path
    main_content = runpy.run_path(main_path,
  File "/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/runpy.py", line 268, in run_path
    return _run_module_code(code, init_globals, run_name,
  File "/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/runpy.py", line 97, in _run_module_code
    _run_code(code, mod_globals, init_globals,
  File "/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/runpy.py", line 87, in _run_code
    exec(code, run_globals)
  File "/Users/<USER>/project/numerology/backend/run.py", line 8, in <module>
    from app.main import app
  File "/Users/<USER>/project/numerology/backend/app/main.py", line 12, in <module>
    from app.routers import horoscope_router, user_router, analysis_router, daily_mood_router, emotion_diary_router
  File "/Users/<USER>/project/numerology/backend/app/routers/daily_mood_router.py", line 14, in <module>
    from app.services.daily_mood_service import DailyMoodService
  File "/Users/<USER>/project/numerology/backend/app/services/daily_mood_service.py", line 8, in <module>
    from app.services.enhanced_horoscope_service import enhanced_horoscope_service
  File "/Users/<USER>/project/numerology/backend/app/services/enhanced_horoscope_service.py", line 507, in <module>
    enhanced_horoscope_service = EnhancedHoroscopeService()
  File "/Users/<USER>/project/numerology/backend/app/services/enhanced_horoscope_service.py", line 124, in __init__
    self.cache_service = HoroscopeCacheService()
TypeError: __init__() missing 1 required positional argument: 'redis_client'
INFO:watchfiles.main:3 changes detected
WARNING:  WatchFiles detected changes in 'migrate_to_mysql.py'. Reloading...
Traceback (most recent call last):
  File "<string>", line 1, in <module>
  File "/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/spawn.py", line 116, in spawn_main
    exitcode = _main(fd, parent_sentinel)
  File "/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/spawn.py", line 125, in _main
    prepare(preparation_data)
  File "/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/spawn.py", line 236, in prepare
    _fixup_main_from_path(data['init_main_from_path'])
  File "/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/spawn.py", line 287, in _fixup_main_from_path
    main_content = runpy.run_path(main_path,
  File "/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/runpy.py", line 268, in run_path
    return _run_module_code(code, init_globals, run_name,
  File "/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/runpy.py", line 97, in _run_module_code
    _run_code(code, mod_globals, init_globals,
  File "/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/runpy.py", line 87, in _run_code
    exec(code, run_globals)
  File "/Users/<USER>/project/numerology/backend/run.py", line 8, in <module>
    from app.main import app
  File "/Users/<USER>/project/numerology/backend/app/main.py", line 12, in <module>
    from app.routers import horoscope_router, user_router, analysis_router, daily_mood_router, emotion_diary_router
  File "/Users/<USER>/project/numerology/backend/app/routers/daily_mood_router.py", line 14, in <module>
    from app.services.daily_mood_service import DailyMoodService
  File "/Users/<USER>/project/numerology/backend/app/services/daily_mood_service.py", line 8, in <module>
    from app.services.enhanced_horoscope_service import enhanced_horoscope_service
  File "/Users/<USER>/project/numerology/backend/app/services/enhanced_horoscope_service.py", line 507, in <module>
    enhanced_horoscope_service = EnhancedHoroscopeService()
  File "/Users/<USER>/project/numerology/backend/app/services/enhanced_horoscope_service.py", line 124, in __init__
    self.cache_service = HoroscopeCacheService()
TypeError: __init__() missing 1 required positional argument: 'redis_client'
INFO:watchfiles.main:3 changes detected
WARNING:  WatchFiles detected changes in 'migrate_to_mysql.py'. Reloading...
Traceback (most recent call last):
  File "<string>", line 1, in <module>
  File "/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/spawn.py", line 116, in spawn_main
    exitcode = _main(fd, parent_sentinel)
  File "/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/spawn.py", line 125, in _main
    prepare(preparation_data)
  File "/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/spawn.py", line 236, in prepare
    _fixup_main_from_path(data['init_main_from_path'])
  File "/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/spawn.py", line 287, in _fixup_main_from_path
    main_content = runpy.run_path(main_path,
  File "/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/runpy.py", line 268, in run_path
    return _run_module_code(code, init_globals, run_name,
  File "/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/runpy.py", line 97, in _run_module_code
    _run_code(code, mod_globals, init_globals,
  File "/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/runpy.py", line 87, in _run_code
    exec(code, run_globals)
  File "/Users/<USER>/project/numerology/backend/run.py", line 8, in <module>
    from app.main import app
  File "/Users/<USER>/project/numerology/backend/app/main.py", line 12, in <module>
    from app.routers import horoscope_router, user_router, analysis_router, daily_mood_router, emotion_diary_router
  File "/Users/<USER>/project/numerology/backend/app/routers/daily_mood_router.py", line 14, in <module>
    from app.services.daily_mood_service import DailyMoodService
  File "/Users/<USER>/project/numerology/backend/app/services/daily_mood_service.py", line 8, in <module>
    from app.services.enhanced_horoscope_service import enhanced_horoscope_service
  File "/Users/<USER>/project/numerology/backend/app/services/enhanced_horoscope_service.py", line 507, in <module>
    enhanced_horoscope_service = EnhancedHoroscopeService()
  File "/Users/<USER>/project/numerology/backend/app/services/enhanced_horoscope_service.py", line 124, in __init__
    self.cache_service = HoroscopeCacheService()
TypeError: __init__() missing 1 required positional argument: 'redis_client'
INFO:watchfiles.main:3 changes detected
WARNING:  WatchFiles detected changes in 'migrate_to_mysql.py'. Reloading...
Traceback (most recent call last):
  File "<string>", line 1, in <module>
  File "/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/spawn.py", line 116, in spawn_main
    exitcode = _main(fd, parent_sentinel)
  File "/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/spawn.py", line 125, in _main
    prepare(preparation_data)
  File "/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/spawn.py", line 236, in prepare
    _fixup_main_from_path(data['init_main_from_path'])
  File "/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/spawn.py", line 287, in _fixup_main_from_path
    main_content = runpy.run_path(main_path,
  File "/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/runpy.py", line 268, in run_path
    return _run_module_code(code, init_globals, run_name,
  File "/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/runpy.py", line 97, in _run_module_code
    _run_code(code, mod_globals, init_globals,
  File "/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/runpy.py", line 87, in _run_code
    exec(code, run_globals)
  File "/Users/<USER>/project/numerology/backend/run.py", line 8, in <module>
    from app.main import app
  File "/Users/<USER>/project/numerology/backend/app/main.py", line 12, in <module>
    from app.routers import horoscope_router, user_router, analysis_router, daily_mood_router, emotion_diary_router
  File "/Users/<USER>/project/numerology/backend/app/routers/daily_mood_router.py", line 14, in <module>
    from app.services.daily_mood_service import DailyMoodService
  File "/Users/<USER>/project/numerology/backend/app/services/daily_mood_service.py", line 8, in <module>
    from app.services.enhanced_horoscope_service import enhanced_horoscope_service
  File "/Users/<USER>/project/numerology/backend/app/services/enhanced_horoscope_service.py", line 507, in <module>
    enhanced_horoscope_service = EnhancedHoroscopeService()
  File "/Users/<USER>/project/numerology/backend/app/services/enhanced_horoscope_service.py", line 124, in __init__
    self.cache_service = HoroscopeCacheService()
TypeError: __init__() missing 1 required positional argument: 'redis_client'
INFO:watchfiles.main:3 changes detected
WARNING:  WatchFiles detected changes in 'migrate_to_mysql.py'. Reloading...
Traceback (most recent call last):
  File "<string>", line 1, in <module>
  File "/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/spawn.py", line 116, in spawn_main
    exitcode = _main(fd, parent_sentinel)
  File "/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/spawn.py", line 125, in _main
    prepare(preparation_data)
  File "/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/spawn.py", line 236, in prepare
    _fixup_main_from_path(data['init_main_from_path'])
  File "/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/spawn.py", line 287, in _fixup_main_from_path
    main_content = runpy.run_path(main_path,
  File "/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/runpy.py", line 268, in run_path
    return _run_module_code(code, init_globals, run_name,
  File "/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/runpy.py", line 97, in _run_module_code
    _run_code(code, mod_globals, init_globals,
  File "/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/runpy.py", line 87, in _run_code
    exec(code, run_globals)
  File "/Users/<USER>/project/numerology/backend/run.py", line 8, in <module>
    from app.main import app
  File "/Users/<USER>/project/numerology/backend/app/main.py", line 12, in <module>
    from app.routers import horoscope_router, user_router, analysis_router, daily_mood_router, emotion_diary_router
  File "/Users/<USER>/project/numerology/backend/app/routers/daily_mood_router.py", line 14, in <module>
    from app.services.daily_mood_service import DailyMoodService
  File "/Users/<USER>/project/numerology/backend/app/services/daily_mood_service.py", line 8, in <module>
    from app.services.enhanced_horoscope_service import enhanced_horoscope_service
  File "/Users/<USER>/project/numerology/backend/app/services/enhanced_horoscope_service.py", line 507, in <module>
    enhanced_horoscope_service = EnhancedHoroscopeService()
  File "/Users/<USER>/project/numerology/backend/app/services/enhanced_horoscope_service.py", line 124, in __init__
    self.cache_service = HoroscopeCacheService()
TypeError: __init__() missing 1 required positional argument: 'redis_client'
INFO:watchfiles.main:3 changes detected
WARNING:  WatchFiles detected changes in 'migrate_to_mysql.py'. Reloading...
Traceback (most recent call last):
  File "<string>", line 1, in <module>
  File "/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/spawn.py", line 116, in spawn_main
    exitcode = _main(fd, parent_sentinel)
  File "/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/spawn.py", line 125, in _main
    prepare(preparation_data)
  File "/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/spawn.py", line 236, in prepare
    _fixup_main_from_path(data['init_main_from_path'])
  File "/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/spawn.py", line 287, in _fixup_main_from_path
    main_content = runpy.run_path(main_path,
  File "/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/runpy.py", line 268, in run_path
    return _run_module_code(code, init_globals, run_name,
  File "/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/runpy.py", line 97, in _run_module_code
    _run_code(code, mod_globals, init_globals,
  File "/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/runpy.py", line 87, in _run_code
    exec(code, run_globals)
  File "/Users/<USER>/project/numerology/backend/run.py", line 8, in <module>
    from app.main import app
  File "/Users/<USER>/project/numerology/backend/app/main.py", line 12, in <module>
    from app.routers import horoscope_router, user_router, analysis_router, daily_mood_router, emotion_diary_router
  File "/Users/<USER>/project/numerology/backend/app/routers/daily_mood_router.py", line 14, in <module>
    from app.services.daily_mood_service import DailyMoodService
  File "/Users/<USER>/project/numerology/backend/app/services/daily_mood_service.py", line 8, in <module>
    from app.services.enhanced_horoscope_service import enhanced_horoscope_service
  File "/Users/<USER>/project/numerology/backend/app/services/enhanced_horoscope_service.py", line 507, in <module>
    enhanced_horoscope_service = EnhancedHoroscopeService()
  File "/Users/<USER>/project/numerology/backend/app/services/enhanced_horoscope_service.py", line 124, in __init__
    self.cache_service = HoroscopeCacheService()
TypeError: __init__() missing 1 required positional argument: 'redis_client'
