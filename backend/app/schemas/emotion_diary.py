"""
情绪日记 Pydantic 架构
"""

from pydantic import BaseModel, Field, validator
from typing import List, Optional, Dict, Any
from datetime import datetime

class EmotionDiaryCreate(BaseModel):
    """情绪日记创建模型"""
    content: str = Field(..., min_length=10, max_length=5000, description="日记内容")
    intensity: int = Field(5, ge=1, le=10, description="情绪强度")
    emotion_tags: List[str] = Field(default_factory=list, description="情绪标签")
    trigger_events: List[str] = Field(default_factory=list, description="触发事件")
    coping_strategies: List[str] = Field(default_factory=list, description="应对策略")
    is_private: bool = Field(True, description="是否私密")

    @validator('content')
    def validate_content(cls, v):
        if not v.strip():
            raise ValueError('日记内容不能为空')
        return v.strip()

    @validator('emotion_tags')
    def validate_emotion_tags(cls, v):
        if len(v) > 15:
            raise ValueError('情绪标签不能超过15个')
        return v

class EmotionDiaryResponse(BaseModel):
    """情绪日记响应模型"""
    success: bool
    message: str
    entry_id: Optional[str] = None
    ai_insights: Optional[str] = None
    sentiment_score: Optional[float] = None
    entry_data: Optional[Dict[str, Any]] = None

class EmotionAnalysis(BaseModel):
    """情绪分析模型"""
    period_days: int
    total_entries: int
    average_intensity: float
    dominant_emotions: List[str]
    emotion_trends: Dict[str, float]
    patterns: List[str]
    recommendations: List[str]