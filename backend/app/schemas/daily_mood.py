"""
Pydantic 架构 - 今日心象和情绪日记
"""

from pydantic import BaseModel, Field, validator
from typing import List, Optional, Dict, Any
from datetime import datetime
from enum import Enum

# 枚举定义
class MoodType(str, Enum):
    excited = "excited"
    happy = "happy"
    calm = "calm"
    focused = "focused"
    tired = "tired"
    stressed = "stressed"

class ActivityType(str, Enum):
    work = "work"
    study = "study"
    exercise = "exercise"
    social = "social"
    rest = "rest"
    reflection = "reflection"

class EnvironmentType(str, Enum):
    home = "home"
    office = "office"
    outdoor = "outdoor"
    transport = "transport"
    social_place = "social_place"

# 今日心象签到相关架构
class DailyMoodCheckIn(BaseModel):
    """今日心象签到请求模型"""
    
    # 核心心情数据
    primary_mood: MoodType = Field(..., description="主要心情状态")
    mood_intensity: int = Field(5, ge=1, le=10, description="心情强度")
    emotional_stability: int = Field(5, ge=1, le=10, description="情绪稳定性")
    
    # 生理指标
    energy_level: int = Field(5, ge=1, le=10, description="精力水平")
    stress_level: int = Field(5, ge=1, le=10, description="压力水平")
    sleep_quality: int = Field(5, ge=1, le=10, description="睡眠质量")
    physical_comfort: int = Field(7, ge=1, le=10, description="身体舒适度")
    health_status: str = Field("良好", description="健康状况")
    
    # 心理状态
    focus_level: int = Field(5, ge=1, le=10, description="专注程度")
    anxiety_level: int = Field(3, ge=1, le=10, description="焦虑水平")
    confidence_level: int = Field(6, ge=1, le=10, description="自信程度")
    motivation_level: int = Field(5, ge=1, le=10, description="动力水平")
    
    # 情境信息
    current_activity: ActivityType = Field(ActivityType.work, description="当前活动")
    environment: EnvironmentType = Field(EnvironmentType.home, description="所在环境")
    social_situation: str = Field("独处", description="社交情况")
    
    # 情绪标签和备注
    emotion_tags: List[str] = Field(default_factory=list, description="情绪标签")
    gratitude_moment: Optional[str] = Field(None, description="感恩时刻")
    daily_intentions: List[str] = Field(default_factory=list, description="今日意图")
    anticipated_challenges: List[str] = Field(default_factory=list, description="预期挑战")
    desired_mood_state: str = Field("positive", description="期望心情状态")

    @validator('emotion_tags')
    def validate_emotion_tags(cls, v):
        if len(v) > 10:
            raise ValueError('情绪标签不能超过10个')
        return v

class DailyMoodResponse(BaseModel):
    """今日心象签到响应模型"""
    success: bool
    message: str
    checkin_id: Optional[str] = None
    mood_score: Optional[float] = None
    streak_days: Optional[int] = None
    recommendations: Optional[List[str]] = None
    checkin_data: Optional[Dict[str, Any]] = None

class MoodAnalysis(BaseModel):
    """心情模式分析模型"""
    period_days: int
    total_checkins: int
    average_mood_score: float
    mood_trend: str  # "improving", "stable", "declining"
    dominant_moods: List[str]
    patterns: List[str]
    recommendations: List[str]
    mood_distribution: Dict[str, int]

# 情绪日记相关架构
class EmotionDiaryCreate(BaseModel):
    """情绪日记创建模型"""
    content: str = Field(..., min_length=10, max_length=5000, description="日记内容")
    intensity: int = Field(5, ge=1, le=10, description="情绪强度")
    emotion_tags: List[str] = Field(default_factory=list, description="情绪标签")
    trigger_events: List[str] = Field(default_factory=list, description="触发事件")
    coping_strategies: List[str] = Field(default_factory=list, description="应对策略")
    is_private: bool = Field(True, description="是否私密")

    @validator('content')
    def validate_content(cls, v):
        if not v.strip():
            raise ValueError('日记内容不能为空')
        return v.strip()

    @validator('emotion_tags')
    def validate_emotion_tags(cls, v):
        if len(v) > 15:
            raise ValueError('情绪标签不能超过15个')
        return v

class EmotionDiaryResponse(BaseModel):
    """情绪日记响应模型"""
    success: bool
    message: str
    entry_id: Optional[str] = None
    ai_insights: Optional[str] = None
    sentiment_score: Optional[float] = None
    entry_data: Optional[Dict[str, Any]] = None

class EmotionAnalysis(BaseModel):
    """情绪分析模型"""
    period_days: int
    total_entries: int
    average_intensity: float
    dominant_emotions: List[str]
    emotion_trends: Dict[str, float]
    patterns: List[str]
    recommendations: List[str]

# 用户状态仪表板
class UserMoodDashboard(BaseModel):
    """用户心象仪表板模型"""
    today_checked_in: bool
    today_mood: Optional[str] = None
    today_score: Optional[float] = None
    streak_days: int
    week_average: Optional[float] = None
    month_trend: str  # "improving", "stable", "declining"
    total_checkins: int
    recent_emotions: List[str]
    achievements: List[str]

# 心情建议模型
class MoodRecommendation(BaseModel):
    """心情建议模型"""
    type: str  # "meditation", "breathing", "activity", "rest"
    title: str
    description: str
    duration_minutes: Optional[int] = None
    difficulty: str  # "easy", "medium", "hard"
    benefits: List[str]
    suitable_moods: List[str]

# 成就系统
class Achievement(BaseModel):
    """成就模型"""
    achievement_id: str
    title: str
    description: str
    icon: str
    category: str  # "streak", "consistency", "growth", "milestone"
    criteria: Dict[str, Any]
    reward_points: int
    unlocked_at: Optional[datetime] = None

class UserStats(BaseModel):
    """用户统计模型"""
    total_checkins: int
    current_streak: int
    longest_streak: int
    total_diary_entries: int
    average_mood_score: float
    most_common_mood: str
    improvement_percentage: float
    achievements_count: int
    days_since_first_checkin: int