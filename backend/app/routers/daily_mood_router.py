"""
今日心象签到路由 - 数字星辰
"""

from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from datetime import datetime, date
from typing import Optional, List

from app.core.database import get_db
from app.core.auth import get_current_user
from app.models.user import User
from app.schemas.daily_mood import DailyMoodCheckIn, DailyMoodResponse, MoodAnalysis
from app.services.daily_mood_service import DailyMoodService

router = APIRouter(prefix="/api/daily-mood", tags=["daily-mood"])

@router.post("/checkin", response_model=DailyMoodResponse)
async def create_mood_checkin(
    mood_data: DailyMoodCheckIn,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """创建今日心象签到记录"""
    try:
        service = DailyMoodService(db)
        
        # 检查今日是否已签到
        today_checkin = service.get_today_checkin(current_user.user_id)
        if today_checkin:
            # 更新今日签到
            result = service.update_checkin(today_checkin.checkin_id, mood_data)
            message = "今日心象已更新"
        else:
            # 创建新签到
            result = service.create_checkin(current_user.user_id, mood_data)
            message = "心象签到成功"
        
        # 计算连续签到天数
        streak_days = service.get_streak_days(current_user.user_id)
        
        # 计算心情评分
        mood_score = service.calculate_mood_score(mood_data)
        
        # 生成个性化建议
        recommendations = service.generate_recommendations(mood_data, current_user)
        
        return DailyMoodResponse(
            success=True,
            message=message,
            checkin_id=result.checkin_id,
            mood_score=mood_score,
            streak_days=streak_days,
            recommendations=recommendations,
            checkin_data=result
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"心象签到失败: {str(e)}"
        )

@router.get("/today", response_model=Optional[DailyMoodResponse])
async def get_today_checkin(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取今日心象签到记录"""
    try:
        service = DailyMoodService(db)
        checkin = service.get_today_checkin(current_user.user_id)
        
        if not checkin:
            return None
            
        return DailyMoodResponse(
            success=True,
            message="获取今日心象记录成功",
            checkin_id=checkin.checkin_id,
            mood_score=service.calculate_mood_score(checkin),
            streak_days=service.get_streak_days(current_user.user_id),
            checkin_data=checkin
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"获取今日心象失败: {str(e)}"
        )

@router.get("/history", response_model=List[DailyMoodResponse])
async def get_mood_history(
    days: int = 30,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取心象签到历史记录"""
    try:
        service = DailyMoodService(db)
        history = service.get_user_history(current_user.user_id, days)
        
        results = []
        for checkin in history:
            results.append(DailyMoodResponse(
                success=True,
                message="历史记录",
                checkin_id=checkin.checkin_id,
                mood_score=service.calculate_mood_score(checkin),
                checkin_data=checkin
            ))
            
        return results
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"获取历史记录失败: {str(e)}"
        )

@router.get("/analysis", response_model=MoodAnalysis)
async def get_mood_analysis(
    days: int = 30,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取心情模式分析"""
    try:
        service = DailyMoodService(db)
        analysis = service.analyze_mood_patterns(current_user.user_id, days)
        
        return MoodAnalysis(
            period_days=days,
            total_checkins=analysis['total_checkins'],
            average_mood_score=analysis['average_mood_score'],
            mood_trend=analysis['mood_trend'],
            dominant_moods=analysis['dominant_moods'],
            patterns=analysis['patterns'],
            recommendations=analysis['recommendations'],
            mood_distribution=analysis['mood_distribution']
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"心情分析失败: {str(e)}"
        )

@router.get("/dashboard")
async def get_mood_dashboard(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取心象仪表板数据"""
    try:
        service = DailyMoodService(db)
        
        # 今日签到状态
        today_checkin = service.get_today_checkin(current_user.user_id)
        
        # 连续签到天数
        streak_days = service.get_streak_days(current_user.user_id)
        
        # 本周平均心情
        week_avg = service.get_period_average(current_user.user_id, 7)
        
        # 本月趋势
        month_trend = service.get_mood_trend(current_user.user_id, 30)
        
        return {
            "today_checked_in": bool(today_checkin),
            "today_mood": today_checkin.primary_mood if today_checkin else None,
            "today_score": service.calculate_mood_score(today_checkin) if today_checkin else None,
            "streak_days": streak_days,
            "week_average": week_avg,
            "month_trend": month_trend,
            "total_checkins": service.get_total_checkins(current_user.user_id)
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"获取仪表板数据失败: {str(e)}"
        )