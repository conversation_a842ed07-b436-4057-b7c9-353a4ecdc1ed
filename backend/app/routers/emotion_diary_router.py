"""
情绪日记路由 - 数字星辰
"""

from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from datetime import datetime
from typing import List, Optional

from app.core.database import get_db
from app.core.auth import get_current_user
from app.models.user import User
from app.schemas.emotion_diary import EmotionDiaryCreate, EmotionDiaryResponse, EmotionAnalysis
from app.services.emotion_diary_service import EmotionDiaryService

router = APIRouter(prefix="/api/emotion-diary", tags=["emotion-diary"])

@router.post("/create", response_model=EmotionDiaryResponse)
async def create_emotion_entry(
    diary_data: EmotionDiaryCreate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """创建情绪日记条目"""
    try:
        service = EmotionDiaryService(db)
        
        # 创建日记条目
        entry = service.create_entry(current_user.user_id, diary_data)
        
        # 生成AI洞察（如果可用）
        ai_insights = None
        try:
            ai_insights = await service.generate_ai_insights(diary_data.content, current_user)
        except Exception as ai_error:
            print(f"AI分析失败: {ai_error}")
        
        return EmotionDiaryResponse(
            success=True,
            message="情绪日记保存成功",
            entry_id=entry.entry_id,
            ai_insights=ai_insights,
            entry_data=entry
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"情绪日记保存失败: {str(e)}"
        )

@router.get("/list", response_model=List[EmotionDiaryResponse])
async def get_emotion_entries(
    limit: int = 20,
    offset: int = 0,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取情绪日记列表"""
    try:
        service = EmotionDiaryService(db)
        entries = service.get_user_entries(current_user.user_id, limit, offset)
        
        results = []
        for entry in entries:
            results.append(EmotionDiaryResponse(
                success=True,
                message="日记条目",
                entry_id=entry.entry_id,
                entry_data=entry
            ))
            
        return results
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"获取日记列表失败: {str(e)}"
        )

@router.get("/{entry_id}", response_model=EmotionDiaryResponse)
async def get_emotion_entry(
    entry_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取特定情绪日记条目"""
    try:
        service = EmotionDiaryService(db)
        entry = service.get_entry_by_id(entry_id, current_user.user_id)
        
        if not entry:
            raise HTTPException(status_code=404, detail="日记条目不存在")
            
        return EmotionDiaryResponse(
            success=True,
            message="获取日记成功",
            entry_id=entry.entry_id,
            entry_data=entry
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"获取日记失败: {str(e)}"
        )

@router.put("/{entry_id}", response_model=EmotionDiaryResponse)
async def update_emotion_entry(
    entry_id: str,
    diary_data: EmotionDiaryCreate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """更新情绪日记条目"""
    try:
        service = EmotionDiaryService(db)
        
        # 验证条目所有权
        existing_entry = service.get_entry_by_id(entry_id, current_user.user_id)
        if not existing_entry:
            raise HTTPException(status_code=404, detail="日记条目不存在")
        
        # 更新条目
        updated_entry = service.update_entry(entry_id, diary_data)
        
        return EmotionDiaryResponse(
            success=True,
            message="日记更新成功",
            entry_id=updated_entry.entry_id,
            entry_data=updated_entry
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"更新日记失败: {str(e)}"
        )

@router.delete("/{entry_id}")
async def delete_emotion_entry(
    entry_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """删除情绪日记条目"""
    try:
        service = EmotionDiaryService(db)
        
        # 验证条目所有权
        existing_entry = service.get_entry_by_id(entry_id, current_user.user_id)
        if not existing_entry:
            raise HTTPException(status_code=404, detail="日记条目不存在")
        
        # 删除条目
        service.delete_entry(entry_id)
        
        return {"success": True, "message": "日记删除成功"}
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"删除日记失败: {str(e)}"
        )

@router.get("/analysis/patterns", response_model=EmotionAnalysis)
async def get_emotion_analysis(
    days: int = 30,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取情绪模式分析"""
    try:
        service = EmotionDiaryService(db)
        analysis = service.analyze_emotion_patterns(current_user.user_id, days)
        
        return EmotionAnalysis(
            period_days=days,
            total_entries=analysis['total_entries'],
            average_intensity=analysis['average_intensity'],
            dominant_emotions=analysis['dominant_emotions'],
            emotion_trends=analysis['emotion_trends'],
            patterns=analysis['patterns'],
            recommendations=analysis['recommendations']
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"情绪分析失败: {str(e)}"
        )

@router.get("/search/by-tag")
async def search_by_emotion_tag(
    tag: str,
    limit: int = 10,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """按情绪标签搜索日记"""
    try:
        service = EmotionDiaryService(db)
        entries = service.search_by_tag(current_user.user_id, tag, limit)
        
        results = []
        for entry in entries:
            results.append({
                "entry_id": entry.entry_id,
                "content_preview": entry.content[:100] + "..." if len(entry.content) > 100 else entry.content,
                "intensity": entry.intensity,
                "emotion_tags": entry.emotion_tags,
                "timestamp": entry.timestamp
            })
            
        return {"results": results, "tag": tag, "count": len(results)}
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"搜索失败: {str(e)}"
        )