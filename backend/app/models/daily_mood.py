"""
数据模型架构 - 今日心象签到
"""

from sqlalchemy import Column, String, Integer, Float, DateTime, Text, JSON, Boolean, ForeignKey
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from app.core.database import Base
import uuid
from datetime import datetime

class DailyMoodCheckin(Base):
    """今日心象签到表"""
    __tablename__ = "daily_mood_checkins"
    
    checkin_id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    user_id = Column(String, ForeignKey("users.user_id"), nullable=False, index=True)
    checkin_date = Column(DateTime, default=datetime.utcnow, nullable=False, index=True)
    
    # 核心心情数据
    primary_mood = Column(String, nullable=False)  # excited, happy, calm, focused, tired, stressed
    mood_intensity = Column(Integer, default=5)  # 1-10
    emotional_stability = Column(Integer, default=5)  # 1-10
    
    # 生理指标
    energy_level = Column(Integer, default=5)  # 1-10
    stress_level = Column(Integer, default=5)  # 1-10
    sleep_quality = Column(Integer, default=5)  # 1-10
    physical_comfort = Column(Integer, default=7)  # 1-10
    health_status = Column(String, default='良好')
    
    # 心理状态
    focus_level = Column(Integer, default=5)  # 1-10
    anxiety_level = Column(Integer, default=3)  # 1-10
    confidence_level = Column(Integer, default=6)  # 1-10
    motivation_level = Column(Integer, default=5)  # 1-10
    
    # 情境信息
    current_activity = Column(String, default='work')
    environment = Column(String, default='home')
    social_situation = Column(String, default='独处')
    
    # 情绪标签和备注
    emotion_tags = Column(JSON, default=list)  # ['快乐', '放松', '专注']
    gratitude_moment = Column(Text, nullable=True)  # 感恩记录
    daily_intentions = Column(JSON, default=list)  # 今日意图
    anticipated_challenges = Column(JSON, default=list)  # 预期挑战
    desired_mood_state = Column(String, default='positive')  # 期望心情状态
    
    # 计算字段
    mood_score = Column(Float, nullable=True)  # 综合心情评分
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关系
    user = relationship("User", back_populates="mood_checkins")

class EmotionDiary(Base):
    """情绪日记表"""
    __tablename__ = "emotion_diaries"
    
    entry_id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    user_id = Column(String, ForeignKey("users.user_id"), nullable=False, index=True)
    
    # 核心内容
    content = Column(Text, nullable=False)  # 日记内容
    intensity = Column(Integer, default=5)  # 情绪强度 1-10
    emotion_tags = Column(JSON, default=list)  # 情绪标签
    
    # AI分析结果
    ai_insights = Column(Text, nullable=True)  # AI洞察
    sentiment_score = Column(Float, nullable=True)  # 情感评分
    emotion_categories = Column(JSON, default=dict)  # 情绪分类
    
    # 上下文信息
    trigger_events = Column(JSON, default=list)  # 触发事件
    coping_strategies = Column(JSON, default=list)  # 应对策略
    
    # 元数据
    word_count = Column(Integer, default=0)
    is_private = Column(Boolean, default=True)
    
    # 时间戳
    timestamp = Column(DateTime, default=datetime.utcnow, index=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关系
    user = relationship("User", back_populates="emotion_diaries")

class MoodStreak(Base):
    """心情签到连续记录表"""
    __tablename__ = "mood_streaks"
    
    streak_id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    user_id = Column(String, ForeignKey("users.user_id"), nullable=False, index=True)
    
    # 连续记录
    current_streak = Column(Integer, default=0)  # 当前连续天数
    longest_streak = Column(Integer, default=0)  # 最长连续天数
    total_checkins = Column(Integer, default=0)  # 总签到次数
    
    # 统计信息
    last_checkin_date = Column(DateTime, nullable=True)
    streak_start_date = Column(DateTime, nullable=True)
    
    # 成就解锁
    achievements = Column(JSON, default=list)  # 已解锁成就
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关系
    user = relationship("User", back_populates="mood_streak")