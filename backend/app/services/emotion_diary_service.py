"""
情绪日记服务 - 数字星辰
"""

from sqlalchemy.orm import Session
from sqlalchemy import desc, func
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
import re
from collections import Counter

from app.models.daily_mood import EmotionDiary
from app.models.user import User
from app.schemas.emotion_diary import EmotionDiaryCreate
from app.services.claude_analysis_service import ClaudeAnalysisService

class EmotionDiaryService:
    """情绪日记服务类"""
    
    def __init__(self, db: Session):
        self.db = db
        self.claude_service = ClaudeAnalysisService()
    
    def create_entry(self, user_id: str, diary_data: EmotionDiaryCreate) -> EmotionDiary:
        """创建情绪日记条目"""
        
        # 计算字数
        word_count = len(re.findall(r'\S+', diary_data.content))
        
        entry = EmotionDiary(
            user_id=user_id,
            content=diary_data.content,
            intensity=diary_data.intensity,
            emotion_tags=diary_data.emotion_tags,
            trigger_events=diary_data.trigger_events,
            coping_strategies=diary_data.coping_strategies,
            is_private=diary_data.is_private,
            word_count=word_count,
            timestamp=datetime.utcnow()
        )
        
        self.db.add(entry)
        self.db.commit()
        self.db.refresh(entry)
        
        return entry
    
    def get_entry_by_id(self, entry_id: str, user_id: str) -> Optional[EmotionDiary]:
        """根据ID获取日记条目"""
        return self.db.query(EmotionDiary).filter(
            EmotionDiary.entry_id == entry_id,
            EmotionDiary.user_id == user_id
        ).first()
    
    def get_user_entries(self, user_id: str, limit: int = 20, offset: int = 0) -> List[EmotionDiary]:
        """获取用户的日记条目列表"""
        return self.db.query(EmotionDiary).filter(
            EmotionDiary.user_id == user_id
        ).order_by(desc(EmotionDiary.timestamp)).offset(offset).limit(limit).all()
    
    def update_entry(self, entry_id: str, diary_data: EmotionDiaryCreate) -> EmotionDiary:
        """更新日记条目"""
        entry = self.db.query(EmotionDiary).filter(
            EmotionDiary.entry_id == entry_id
        ).first()
        
        if entry:
            entry.content = diary_data.content
            entry.intensity = diary_data.intensity
            entry.emotion_tags = diary_data.emotion_tags
            entry.trigger_events = diary_data.trigger_events
            entry.coping_strategies = diary_data.coping_strategies
            entry.is_private = diary_data.is_private
            entry.word_count = len(re.findall(r'\S+', diary_data.content))
            entry.updated_at = datetime.utcnow()
            
            self.db.commit()
            self.db.refresh(entry)
        
        return entry
    
    def delete_entry(self, entry_id: str) -> bool:
        """删除日记条目"""
        entry = self.db.query(EmotionDiary).filter(
            EmotionDiary.entry_id == entry_id
        ).first()
        
        if entry:
            self.db.delete(entry)
            self.db.commit()
            return True
        
        return False
    
    def search_by_tag(self, user_id: str, tag: str, limit: int = 10) -> List[EmotionDiary]:
        """按标签搜索日记"""
        return self.db.query(EmotionDiary).filter(
            EmotionDiary.user_id == user_id,
            EmotionDiary.emotion_tags.contains([tag])
        ).order_by(desc(EmotionDiary.timestamp)).limit(limit).all()
    
    def analyze_emotion_patterns(self, user_id: str, days: int = 30) -> Dict[str, Any]:
        """分析用户情绪模式"""
        
        # 获取指定时间段内的条目
        since_date = datetime.utcnow() - timedelta(days=days)
        entries = self.db.query(EmotionDiary).filter(
            EmotionDiary.user_id == user_id,
            EmotionDiary.timestamp >= since_date
        ).all()
        
        if not entries:
            return {
                'total_entries': 0,
                'average_intensity': 0,
                'dominant_emotions': [],
                'emotion_trends': {},
                'patterns': [],
                'recommendations': []
            }
        
        # 基础统计
        total_entries = len(entries)
        average_intensity = sum(entry.intensity for entry in entries) / total_entries
        
        # 收集所有情绪标签
        all_tags = []
        for entry in entries:
            all_tags.extend(entry.emotion_tags)
        
        # 统计情绪频率
        emotion_counter = Counter(all_tags)
        dominant_emotions = [emotion for emotion, count in emotion_counter.most_common(5)]
        
        # 情绪趋势分析
        emotion_trends = {}
        for emotion in dominant_emotions:
            trend_data = []
            for entry in entries:
                if emotion in entry.emotion_tags:
                    trend_data.append(entry.intensity)
            
            if trend_data:
                emotion_trends[emotion] = sum(trend_data) / len(trend_data)
        
        # 模式识别
        patterns = self._identify_patterns(entries)
        
        # 生成建议
        recommendations = self._generate_recommendations(entries, average_intensity, dominant_emotions)
        
        return {
            'total_entries': total_entries,
            'average_intensity': round(average_intensity, 2),
            'dominant_emotions': dominant_emotions,
            'emotion_trends': emotion_trends,
            'patterns': patterns,
            'recommendations': recommendations
        }
    
    def _identify_patterns(self, entries: List[EmotionDiary]) -> List[str]:
        """识别情绪模式"""
        patterns = []
        
        # 按时间段分析
        morning_entries = []
        evening_entries = []
        
        for entry in entries:
            hour = entry.timestamp.hour
            if 6 <= hour < 12:
                morning_entries.append(entry)
            elif 18 <= hour <= 23:
                evening_entries.append(entry)
        
        # 分析时间段模式
        if morning_entries:
            morning_avg = sum(e.intensity for e in morning_entries) / len(morning_entries)
            if morning_avg < 5:
                patterns.append("早晨情绪偏低，建议增加晨间正念练习")
        
        if evening_entries:
            evening_avg = sum(e.intensity for e in evening_entries) / len(evening_entries)
            if evening_avg > 7:
                patterns.append("晚间情绪较为积极，适合回顾和感恩练习")
        
        # 分析情绪强度变化
        intensities = [entry.intensity for entry in entries[-7:]]  # 最近7天
        if len(intensities) >= 3:
            if all(intensities[i] < intensities[i+1] for i in range(len(intensities)-1)):
                patterns.append("情绪呈现持续上升趋势，状态良好")
            elif all(intensities[i] > intensities[i+1] for i in range(len(intensities)-1)):
                patterns.append("情绪呈现下降趋势，建议关注自我关怀")
        
        # 分析高频情绪
        recent_tags = []
        for entry in entries[-5:]:  # 最近5篇
            recent_tags.extend(entry.emotion_tags)
        
        tag_counter = Counter(recent_tags)
        if tag_counter:
            most_common_emotion = tag_counter.most_common(1)[0][0]
            if most_common_emotion in ['焦虑', '压力', '疲惫']:
                patterns.append(f"近期'{most_common_emotion}'情绪较为频繁，建议增加放松活动")
        
        return patterns if patterns else ["情绪状态相对稳定，继续保持良好的记录习惯"]
    
    def _generate_recommendations(self, entries: List[EmotionDiary], avg_intensity: float, dominant_emotions: List[str]) -> List[str]:
        """生成个性化建议"""
        recommendations = []
        
        # 基于平均强度的建议
        if avg_intensity < 4:
            recommendations.append("整体情绪偏低，建议增加户外活动和社交互动")
        elif avg_intensity > 7:
            recommendations.append("情绪状态良好，继续保持积极的生活方式")
        else:
            recommendations.append("情绪状态相对稳定，可以尝试新的兴趣爱好")
        
        # 基于主要情绪的建议
        negative_emotions = ['焦虑', '压力', '愤怒', '悲伤', '孤独', '疲惫']
        positive_emotions = ['快乐', '平静', '感激', '兴奋', '满足']
        
        negative_count = sum(1 for emotion in dominant_emotions if emotion in negative_emotions)
        positive_count = sum(1 for emotion in dominant_emotions if emotion in positive_emotions)
        
        if negative_count > positive_count:
            recommendations.append("负面情绪较多，建议尝试冥想、深呼吸或寻求专业支持")
        elif positive_count > negative_count:
            recommendations.append("积极情绪占主导，可以分享你的经验帮助他人")
        
        # 基于记录频率的建议
        if len(entries) < 5:  # 在时间段内记录较少
            recommendations.append("保持规律的情绪记录，有助于更好地了解自己")
        
        # 基于内容长度的建议
        avg_word_count = sum(entry.word_count for entry in entries) / len(entries)
        if avg_word_count < 50:
            recommendations.append("可以尝试写得更详细一些，深度表达有助于情绪释放")
        
        return recommendations
    
    async def generate_ai_insights(self, content: str, user: User) -> Optional[str]:
        """生成AI情绪洞察"""
        try:
            # 构建分析提示
            analysis_prompt = f"""
            请分析以下情绪日记内容，提供温暖、专业的心理洞察：

            用户情绪记录：
            {content}

            请从以下角度提供分析：
            1. 情绪状态解读
            2. 可能的内在需求
            3. 积极的成长点
            4. 温暖的鼓励和建议

            请用温暖、理解的语调，提供2-3句简洁而有深度的洞察。
            """
            
            # 调用Claude分析
            insights = await self.claude_service.analyze_emotion_content(
                content=content,
                user_context={
                    'nickname': user.nickname,
                    'vip_level': user.vip_level
                }
            )
            
            return insights
            
        except Exception as e:
            print(f"AI洞察生成失败: {e}")
            return None