#!/usr/bin/env python3
"""
数据库迁移脚本 - 从SQLite迁移到MySQL
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy import create_engine, text
from app.core.config import settings
# Force MySQL connection
MYSQL_URL = "mysql+pymysql://root:root@localhost/numerology"
from app.models.user import Base, User, UserToken, LoginLog
from app.models.daily_mood import DailyMoodCheckin, EmotionDiary, MoodStreak

def create_mysql_tables():
    """创建MySQL数据库表"""
    print("开始创建MySQL数据库表...")
    
    # 创建MySQL引擎
    mysql_engine = create_engine(MYSQL_URL, echo=True)
    
    try:
        # 创建所有表
        Base.metadata.create_all(bind=mysql_engine)
        print("✅ MySQL数据库表创建成功!")
        
        # 验证表是否创建
        with mysql_engine.connect() as conn:
            result = conn.execute(text("SHOW TABLES"))
            tables = [row[0] for row in result]
            print(f"✅ 创建的表: {', '.join(tables)}")
            
        return True
        
    except Exception as e:
        print(f"❌ 创建MySQL表失败: {e}")
        return False
    finally:
        mysql_engine.dispose()

def verify_database_structure():
    """验证数据库结构"""
    print("\n开始验证数据库结构...")
    
    mysql_engine = create_engine(MYSQL_URL, echo=False)
    
    try:
        with mysql_engine.connect() as conn:
            # 检查用户表结构
            result = conn.execute(text("DESCRIBE users"))
            user_columns = [row[0] for row in result]
            print(f"✅ users表字段: {', '.join(user_columns)}")
            
            # 检查心情签到表结构
            result = conn.execute(text("DESCRIBE daily_mood_checkins"))
            mood_columns = [row[0] for row in result]
            print(f"✅ daily_mood_checkins表字段: {', '.join(mood_columns)}")
            
            # 检查情绪日记表结构
            result = conn.execute(text("DESCRIBE emotion_diaries"))
            diary_columns = [row[0] for row in result]
            print(f"✅ emotion_diaries表字段: {', '.join(diary_columns)}")
            
        print("✅ 数据库结构验证完成!")
        return True
        
    except Exception as e:
        print(f"❌ 验证数据库结构失败: {e}")
        return False
    finally:
        mysql_engine.dispose()

def test_database_operations():
    """测试数据库基本操作"""
    print("\n开始测试数据库基本操作...")
    
    mysql_engine = create_engine(MYSQL_URL, echo=False)
    
    try:
        with mysql_engine.connect() as conn:
            # 测试插入用户
            test_user_sql = text("""
                INSERT INTO users (user_id, username, password_hash, created_at) 
                VALUES ('test_001', 'test_user', 'hashed_password', NOW())
                ON DUPLICATE KEY UPDATE username = VALUES(username)
            """)
            conn.execute(test_user_sql)
            
            # 测试查询用户
            result = conn.execute(text("SELECT user_id, username FROM users WHERE user_id = 'test_001'"))
            user = result.fetchone()
            if user:
                print(f"✅ 测试用户创建成功: {user[0]} - {user[1]}")
            
            # 清理测试数据
            conn.execute(text("DELETE FROM users WHERE user_id = 'test_001'"))
            conn.commit()
            
        print("✅ 数据库操作测试完成!")
        return True
        
    except Exception as e:
        print(f"❌ 数据库操作测试失败: {e}")
        return False
    finally:
        mysql_engine.dispose()

def main():
    """主函数"""
    print("🚀 开始MySQL数据库迁移...")
    print(f"数据库连接: {MYSQL_URL}")
    
    # 步骤1: 创建表
    if not create_mysql_tables():
        print("❌ 迁移失败: 无法创建数据库表")
        return False
    
    # 步骤2: 验证结构
    if not verify_database_structure():
        print("❌ 迁移失败: 数据库结构验证失败")
        return False
    
    # 步骤3: 测试操作
    if not test_database_operations():
        print("❌ 迁移失败: 数据库操作测试失败")
        return False
    
    print("\n🎉 MySQL数据库迁移成功!")
    print("📋 迁移完成的表:")
    print("   - users (用户表)")
    print("   - user_tokens (用户令牌表)")
    print("   - login_logs (登录日志表)")
    print("   - daily_mood_checkins (心情签到表)")
    print("   - emotion_diaries (情绪日记表)")
    print("   - mood_streaks (连续签到表)")
    print("\n✅ 现在可以启动应用并连接到MySQL数据库!")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)