<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数字星辰 - 探索内在无限可能</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            --secondary-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            --accent-gradient: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
            --dark-gradient: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            --glass-bg: rgba(255, 255, 255, 0.1);
            --glass-border: rgba(255, 255, 255, 0.2);
            --shadow-light: 0 8px 32px rgba(31, 38, 135, 0.37);
            --shadow-heavy: 0 20px 60px rgba(31, 38, 135, 0.5);
            --text-primary: #2d3748;
            --text-secondary: #4a5568;
            --text-light: rgba(255, 255, 255, 0.9);
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: var(--primary-gradient);
            min-height: 100vh;
            color: var(--text-primary);
            overflow-x: hidden;
            position: relative;
        }

        /* 动态背景粒子 */
        .particles {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 1;
        }

        .particle {
            position: absolute;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            animation: float 15s infinite ease-in-out;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0) rotate(0deg); opacity: 0; }
            25% { opacity: 1; }
            50% { transform: translateY(-100vh) rotate(180deg); opacity: 0.8; }
            75% { opacity: 1; }
        }

        /* 顶部导航 */
        .top-nav {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid var(--glass-border);
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            box-shadow: var(--shadow-light);
        }

        .logo {
            font-size: 22px;
            font-weight: 800;
            background: var(--secondary-gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .logo::before {
            content: "✨";
            font-size: 24px;
            animation: sparkle 2s ease-in-out infinite;
        }

        @keyframes sparkle {
            0%, 100% { transform: scale(1) rotate(0deg); }
            50% { transform: scale(1.2) rotate(180deg); }
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .user-level {
            background: var(--accent-gradient);
            color: white;
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .avatar {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: var(--secondary-gradient);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 16px;
            box-shadow: var(--shadow-light);
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .avatar:hover {
            transform: scale(1.1) rotate(5deg);
            box-shadow: var(--shadow-heavy);
        }

        /* 主容器 */
        .main-container {
            margin-top: 80px;
            padding-bottom: 100px;
            position: relative;
            z-index: 10;
        }

        /* 今日心象弹窗 */
        .mood-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.7);
            backdrop-filter: blur(10px);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 2000;
            opacity: 0;
            visibility: hidden;
            transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .mood-modal.show {
            opacity: 1;
            visibility: visible;
        }

        .mood-modal-content {
            background: var(--glass-bg);
            backdrop-filter: blur(30px);
            border: 1px solid var(--glass-border);
            margin: 20px;
            border-radius: 30px;
            padding: 40px;
            max-width: 450px;
            width: 100%;
            text-align: center;
            transform: scale(0.7) translateY(50px);
            transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: var(--shadow-heavy);
        }

        .mood-modal.show .mood-modal-content {
            transform: scale(1) translateY(0);
        }

        .mood-title {
            font-size: 28px;
            font-weight: 800;
            margin-bottom: 10px;
            background: var(--secondary-gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .mood-subtitle {
            color: var(--text-light);
            margin-bottom: 30px;
            font-size: 16px;
            line-height: 1.5;
        }

        .mood-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 15px;
            margin-bottom: 30px;
        }

        .mood-item {
            padding: 20px 15px;
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }

        .mood-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.6s;
        }

        .mood-item:hover::before {
            left: 100%;
        }

        .mood-item:hover, .mood-item.selected {
            transform: translateY(-5px) scale(1.05);
            box-shadow: var(--shadow-heavy);
            background: var(--glass-bg);
            border-color: rgba(255, 255, 255, 0.4);
        }

        .mood-emoji {
            font-size: 32px;
            margin-bottom: 8px;
            display: block;
            animation: bounce 2s ease-in-out infinite;
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
            40% { transform: translateY(-10px); }
            60% { transform: translateY(-5px); }
        }

        .mood-label {
            font-size: 14px;
            font-weight: 600;
            color: var(--text-light);
        }

        /* 欢迎区域 */
        .welcome-section {
            padding: 40px 20px;
            text-align: center;
            color: white;
            position: relative;
        }

        .welcome-title {
            font-size: 32px;
            font-weight: 900;
            margin-bottom: 15px;
            background: linear-gradient(45deg, #fff, #f093fb, #4facfe);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            animation: glow 3s ease-in-out infinite alternate;
        }

        @keyframes glow {
            from { filter: drop-shadow(0 0 10px rgba(255, 255, 255, 0.3)); }
            to { filter: drop-shadow(0 0 20px rgba(255, 255, 255, 0.6)); }
        }

        .welcome-subtitle {
            font-size: 18px;
            opacity: 0.9;
            margin-bottom: 25px;
            font-weight: 300;
            letter-spacing: 0.5px;
        }

        /* 用户状态面板 */
        .status-panel {
            background: var(--glass-bg);
            backdrop-filter: blur(30px);
            border: 1px solid var(--glass-border);
            border-radius: 25px;
            padding: 25px;
            margin: 20px;
            box-shadow: var(--shadow-light);
            position: relative;
            overflow: hidden;
        }

        .status-panel::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: conic-gradient(from 0deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            animation: rotate 20s linear infinite;
            pointer-events: none;
        }

        @keyframes rotate {
            to { transform: rotate(360deg); }
        }

        .status-panel-content {
            position: relative;
            z-index: 1;
        }

        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding: 10px 0;
        }

        .status-item:last-child {
            margin-bottom: 0;
        }

        .status-label {
            color: var(--text-light);
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .status-value {
            color: white;
            font-weight: bold;
            font-size: 16px;
        }

        /* 功能模块 */
        .feature-sections {
            padding: 20px;
        }

        .section-header {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 20px;
            padding-left: 5px;
        }

        .section-icon {
            font-size: 24px;
            animation: pulse 2s ease-in-out infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }

        .section-title {
            font-size: 22px;
            font-weight: 800;
            background: var(--secondary-gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
            gap: 15px;
            margin-bottom: 35px;
        }

        .feature-card {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: 25px;
            padding: 25px 20px;
            text-align: center;
            cursor: pointer;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: var(--shadow-light);
            position: relative;
            overflow: hidden;
            height: 180px;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .feature-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: var(--secondary-gradient);
            opacity: 0;
            transition: opacity 0.4s ease;
        }

        .feature-card:hover {
            transform: translateY(-10px) rotateX(5deg);
            box-shadow: var(--shadow-heavy);
        }

        .feature-card:hover::before {
            opacity: 0.1;
        }

        .feature-card-content {
            position: relative;
            z-index: 1;
        }

        .feature-icon {
            font-size: 40px;
            margin-bottom: 15px;
            display: block;
            filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1));
            animation: float-icon 6s ease-in-out infinite;
        }

        @keyframes float-icon {
            0%, 100% { transform: translateY(0) rotate(0deg); }
            50% { transform: translateY(-5px) rotate(5deg); }
        }

        .feature-title {
            font-size: 16px;
            font-weight: 700;
            color: white;
            margin-bottom: 8px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .feature-desc {
            font-size: 12px;
            color: var(--text-light);
            line-height: 1.4;
            opacity: 0.8;
        }

        /* 大卡片样式 */
        .large-card {
            grid-column: span 2;
            height: 220px;
            padding: 30px 25px;
        }

        .large-card .feature-icon {
            font-size: 48px;
        }

        .large-card .feature-title {
            font-size: 20px;
            margin-bottom: 12px;
        }

        .large-card .feature-desc {
            font-size: 14px;
        }

        /* 底部导航 */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: var(--glass-bg);
            backdrop-filter: blur(30px);
            border-top: 1px solid var(--glass-border);
            padding: 15px 0 25px 0;
            z-index: 1000;
            box-shadow: 0 -8px 32px rgba(31, 38, 135, 0.37);
        }

        .nav-tabs {
            display: flex;
            justify-content: space-around;
            align-items: center;
            max-width: 500px;
            margin: 0 auto;
            padding: 0 20px;
        }

        .nav-tab {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 8px 12px;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            border-radius: 15px;
            position: relative;
        }

        .nav-tab::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: var(--secondary-gradient);
            border-radius: 15px;
            opacity: 0;
            transform: scale(0.8);
            transition: all 0.3s ease;
        }

        .nav-tab.active::before {
            opacity: 0.2;
            transform: scale(1);
        }

        .nav-tab-content {
            position: relative;
            z-index: 1;
        }

        .nav-icon {
            font-size: 22px;
            margin-bottom: 4px;
            transition: all 0.3s ease;
            filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
        }

        .nav-tab.active .nav-icon {
            transform: scale(1.1);
            animation: bounce-tab 0.6s ease;
        }

        @keyframes bounce-tab {
            0%, 100% { transform: scale(1.1) translateY(0); }
            50% { transform: scale(1.2) translateY(-3px); }
        }

        .nav-label {
            font-size: 10px;
            font-weight: 600;
            color: var(--text-light);
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .nav-tab.active .nav-label {
            color: white;
        }

        /* 页面容器 */
        .page {
            display: none;
            min-height: calc(100vh - 160px);
            animation: fadeInUp 0.6s ease forwards;
        }

        .page.active {
            display: block;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* 按钮样式 */
        .btn {
            background: var(--secondary-gradient);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 16px;
            font-weight: 700;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: var(--shadow-light);
            position: relative;
            overflow: hidden;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            transition: left 0.6s;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-heavy);
        }

        .btn:hover::before {
            left: 100%;
        }

        .btn-outline {
            background: transparent;
            border: 2px solid rgba(255, 255, 255, 0.3);
            color: white;
        }

        .btn-outline:hover {
            background: var(--secondary-gradient);
            border-color: transparent;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .welcome-title {
                font-size: 26px;
            }
            
            .feature-grid {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .large-card {
                grid-column: span 2;
                height: 200px;
            }
            
            .mood-grid {
                grid-template-columns: repeat(2, 1fr);
            }

            .nav-tabs {
                padding: 0 10px;
            }
        }

        @media (max-width: 480px) {
            .feature-grid {
                grid-template-columns: 1fr;
            }
            
            .large-card {
                grid-column: span 1;
            }
        }

        /* 加载动画 */
        .loading-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-top: 2px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* 隐藏类 */
        .hidden {
            display: none !important;
        }
    </style>
</head>
<body>
    <!-- 动态背景粒子 -->
    <div class="particles" id="particles"></div>

    <!-- 顶部导航 -->
    <div class="top-nav">
        <div class="logo">数字星辰</div>
        <div class="user-info">
            <div class="user-level" id="userLevel">金卡VIP</div>
            <div class="avatar" id="userAvatar">U</div>
        </div>
    </div>

    <!-- 今日心象弹窗 -->
    <div class="mood-modal" id="moodModal">
        <div class="mood-modal-content">
            <div class="mood-title">✨ 今日心象</div>
            <div class="mood-subtitle">记录此刻的内心状态，开启探索之旅</div>
            
            <div class="mood-grid">
                <div class="mood-item" data-mood="excited">
                    <div class="mood-emoji">🎉</div>
                    <div class="mood-label">兴奋</div>
                </div>
                <div class="mood-item" data-mood="happy">
                    <div class="mood-emoji">😊</div>
                    <div class="mood-label">开心</div>
                </div>
                <div class="mood-item" data-mood="calm">
                    <div class="mood-emoji">😌</div>
                    <div class="mood-label">平静</div>
                </div>
                <div class="mood-item" data-mood="focused">
                    <div class="mood-emoji">🎯</div>
                    <div class="mood-label">专注</div>
                </div>
                <div class="mood-item" data-mood="tired">
                    <div class="mood-emoji">😴</div>
                    <div class="mood-label">疲惫</div>
                </div>
                <div class="mood-item" data-mood="stressed">
                    <div class="mood-emoji">😰</div>
                    <div class="mood-label">焦虑</div>
                </div>
            </div>
            
            <div style="display: flex; gap: 15px;">
                <button class="btn btn-outline" onclick="skipMoodCheckin()">稍后再说</button>
                <button class="btn" onclick="submitMoodCheckin()">完成签到</button>
            </div>
        </div>
    </div>

    <!-- 主容器 -->
    <div class="main-container">
        <!-- 首页 -->
        <div class="page active" id="homePage">
            <!-- 欢迎区域 -->
            <div class="welcome-section">
                <div class="welcome-title">你好，探索者</div>
                <div class="welcome-subtitle">在数字与星辰的指引下，发现内在无限可能</div>
            </div>

            <!-- 用户状态面板 -->
            <div class="status-panel">
                <div class="status-panel-content">
                    <div class="status-item">
                        <span class="status-label">
                            <span>🌟</span> 今日运势
                        </span>
                        <span class="status-value">⭐⭐⭐⭐☆</span>
                    </div>
                    <div class="status-item">
                        <span class="status-label">
                            <span>💭</span> 心情指数
                        </span>
                        <span class="status-value" id="moodStatus">等待记录</span>
                    </div>
                    <div class="status-item">
                        <span class="status-label">
                            <span>👑</span> 会员等级
                        </span>
                        <span class="status-value">金卡VIP</span>
                    </div>
                    <div class="status-item">
                        <span class="status-label">
                            <span>🔥</span> 连续签到
                        </span>
                        <span class="status-value" id="streakDays">3天</span>
                    </div>
                </div>
            </div>

            <!-- 功能模块 -->
            <div class="feature-sections">
                <!-- 星象洞察 -->
                <div class="section-header">
                    <div class="section-icon">✨</div>
                    <div class="section-title">星象洞察</div>
                </div>
                <div class="feature-grid">
                    <div class="feature-card" onclick="navigateToPage('astro')">
                        <div class="feature-card-content">
                            <div class="feature-icon">🌟</div>
                            <div class="feature-title">今日运势</div>
                            <div class="feature-desc">专属星座运势解读</div>
                        </div>
                    </div>
                    <div class="feature-card" onclick="showBirthChart()">
                        <div class="feature-card-content">
                            <div class="feature-icon">🎯</div>
                            <div class="feature-title">出生星图</div>
                            <div class="feature-desc">解析命盘密码</div>
                        </div>
                    </div>
                    <div class="feature-card large-card" onclick="showAstroCalendar()">
                        <div class="feature-card-content">
                            <div class="feature-icon">📅</div>
                            <div class="feature-title">星象日历</div>
                            <div class="feature-desc">水逆提醒 · 月相追踪 · 天象预告</div>
                        </div>
                    </div>
                </div>

                <!-- 内在探索 -->
                <div class="section-header">
                    <div class="section-icon">🧠</div>
                    <div class="section-title">内在探索</div>
                </div>
                <div class="feature-grid">
                    <div class="feature-card" onclick="navigateToPage('psychology')">
                        <div class="feature-card-content">
                            <div class="feature-icon">🔍</div>
                            <div class="feature-title">人格测试</div>
                            <div class="feature-desc">MBTI · 九型人格</div>
                        </div>
                    </div>
                    <div class="feature-card" onclick="showFamilyAnalysis()">
                        <div class="feature-card-content">
                            <div class="feature-icon">💝</div>
                            <div class="feature-title">情感模式</div>
                            <div class="feature-desc">原生家庭分析</div>
                        </div>
                    </div>
                </div>

                <!-- 身心调和 -->
                <div class="section-header">
                    <div class="section-icon">🧘‍♀️</div>
                    <div class="section-title">身心调和</div>
                </div>
                <div class="feature-grid">
                    <div class="feature-card" onclick="navigateToPage('meditation')">
                        <div class="feature-card-content">
                            <div class="feature-icon">🎵</div>
                            <div class="feature-title">冥想空间</div>
                            <div class="feature-desc">入睡 · 减压 · 专注</div>
                        </div>
                    </div>
                    <div class="feature-card" onclick="showBreathing()">
                        <div class="feature-card-content">
                            <div class="feature-icon">💨</div>
                            <div class="feature-title">呼吸训练</div>
                            <div class="feature-desc">HRV优化调律</div>
                        </div>
                    </div>
                    <div class="feature-card large-card" onclick="showEmotionDiary()">
                        <div class="feature-card-content">
                            <div class="feature-icon">📝</div>
                            <div class="feature-title">情绪日记</div>
                            <div class="feature-desc">AI智能分析 · 情绪模式追踪 · 个性化建议</div>
                        </div>
                    </div>
                </div>

                <!-- 智能洞察 -->
                <div class="section-header">
                    <div class="section-icon">🤖</div>
                    <div class="section-title">AI洞察</div>
                </div>
                <div class="feature-grid">
                    <div class="feature-card large-card" onclick="navigateToPage('ai')">
                        <div class="feature-card-content">
                            <div class="feature-icon">✨</div>
                            <div class="feature-title">综合分析</div>
                            <div class="feature-desc">整合命理 · 心理 · 行为数据，生成专属洞察报告</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 其他页面占位 -->
        <div class="page" id="astroPage">
            <div style="padding: 40px 20px; text-align: center; color: white;">
                <h2>🌟 星象洞察</h2>
                <p>功能开发中...</p>
            </div>
        </div>

        <div class="page" id="psychologyPage">
            <div style="padding: 40px 20px; text-align: center; color: white;">
                <h2>🧠 心理测试</h2>
                <p>功能开发中...</p>
            </div>
        </div>

        <div class="page" id="meditationPage">
            <div style="padding: 40px 20px; text-align: center; color: white;">
                <h2>🧘‍♀️ 冥想空间</h2>
                <p>功能开发中...</p>
            </div>
        </div>

        <div class="page" id="aiPage">
            <div style="padding: 40px 20px; text-align: center; color: white;">
                <h2>🤖 AI洞察</h2>
                <p>功能开发中...</p>
            </div>
        </div>

        <div class="page" id="profilePage">
            <div style="padding: 40px 20px; text-align: center; color: white;">
                <h2>👤 个人中心</h2>
                <p>功能开发中...</p>
            </div>
        </div>
    </div>

    <!-- 底部导航 -->
    <div class="bottom-nav">
        <div class="nav-tabs">
            <div class="nav-tab active" onclick="navigateToPage('home')">
                <div class="nav-tab-content">
                    <div class="nav-icon">🏠</div>
                    <div class="nav-label">首页</div>
                </div>
            </div>
            <div class="nav-tab" onclick="navigateToPage('astro')">
                <div class="nav-tab-content">
                    <div class="nav-icon">⭐</div>
                    <div class="nav-label">星象</div>
                </div>
            </div>
            <div class="nav-tab" onclick="navigateToPage('psychology')">
                <div class="nav-tab-content">
                    <div class="nav-icon">🧠</div>
                    <div class="nav-label">心理</div>
                </div>
            </div>
            <div class="nav-tab" onclick="navigateToPage('meditation')">
                <div class="nav-tab-content">
                    <div class="nav-icon">🧘‍♀️</div>
                    <div class="nav-label">冥想</div>
                </div>
            </div>
            <div class="nav-tab" onclick="navigateToPage('profile')">
                <div class="nav-tab-content">
                    <div class="nav-icon">👤</div>
                    <div class="nav-label">我的</div>
                </div>
            </div>
        </div>
    </div>

    <script src="app.js"></script>
</body>
</html>