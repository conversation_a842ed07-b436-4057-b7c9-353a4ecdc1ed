// 数字星辰 - 年轻化时尚动态界面
const API_BASE_URL = 'http://localhost:8000';
let currentUser = null;
let selectedMood = null;
let authToken = null;

// 应用配置
const CONFIG = {
    PARTICLES_COUNT: 50,
    API_TIMEOUT: 10000,
    ANIMATION_DURATION: 300
};

// 页面初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

// 应用初始化
async function initializeApp() {
    showLoadingOverlay();
    
    try {
        // 初始化动态背景
        initParticles();
        
        // 检查认证状态
        await checkAuthStatus();
        
        // 初始化用户界面
        setupUserInterface();
        
        // 设置事件监听器
        setupEventListeners();
        
        // 显示今日心象弹窗
        showMoodModal();
        
        // 加载用户数据
        await loadUserData();
        
    } catch (error) {
        console.error('应用初始化失败:', error);
        showNotification('应用加载失败，请刷新重试', 'error');
    } finally {
        hideLoadingOverlay();
    }
}

// 初始化动态背景粒子
function initParticles() {
    const particlesContainer = document.getElementById('particles');
    
    for (let i = 0; i < CONFIG.PARTICLES_COUNT; i++) {
        const particle = document.createElement('div');
        particle.className = 'particle';
        
        // 随机大小和位置
        const size = Math.random() * 4 + 2;
        particle.style.width = size + 'px';
        particle.style.height = size + 'px';
        particle.style.left = Math.random() * 100 + '%';
        particle.style.animationDelay = Math.random() * 15 + 's';
        particle.style.animationDuration = (Math.random() * 10 + 15) + 's';
        
        particlesContainer.appendChild(particle);
    }
}

// 检查认证状态
async function checkAuthStatus() {
    authToken = localStorage.getItem('authToken');
    const userInfo = localStorage.getItem('userInfo');
    
    if (authToken && userInfo) {
        try {
            currentUser = JSON.parse(userInfo);
            // 验证token有效性
            const response = await apiRequest('/api/auth/me');
            if (response) {
                currentUser = response.user;
                updateUserDisplay();
                return;
            }
        } catch (error) {
            console.log('Token验证失败，使用游客模式');
            // Token失效，清除本地存储
            localStorage.removeItem('authToken');
            localStorage.removeItem('userInfo');
            authToken = null;
        }
    }
    
    // 自动登录测试用户
    await autoLogin();
}

// 自动登录测试用户
async function autoLogin() {
    try {
        const response = await apiRequest('/api/auth/login', {
            method: 'POST',
            body: JSON.stringify({
                identifier: 'testuser1',
                password: 'password123',
                login_type: 'password'
            })
        });
        
        if (response && response.tokens) {
            authToken = response.tokens.access_token;
            currentUser = response.user;
            
            localStorage.setItem('authToken', authToken);
            localStorage.setItem('userInfo', JSON.stringify(currentUser));
            
            updateUserDisplay();
        }
    } catch (error) {
        console.log('自动登录失败，使用模拟用户');
        // 使用模拟用户数据
        currentUser = {
            user_id: 'demo',
            username: 'testuser1',
            nickname: '探索者',
            vip_level: 'gold',
            zodiac_sign: '金牛座'
        };
        updateUserDisplay();
    }
}

// 更新用户显示
function updateUserDisplay() {
    if (currentUser) {
        const avatar = document.getElementById('userAvatar');
        const userLevel = document.getElementById('userLevel');
        
        avatar.textContent = (currentUser.nickname || currentUser.username || 'U')[0].toUpperCase();
        
        if (currentUser.vip_level) {
            const levelMap = {
                'basic': '基础会员',
                'silver': '银卡VIP',
                'gold': '金卡VIP', 
                'platinum': '白金VIP'
            };
            userLevel.textContent = levelMap[currentUser.vip_level] || '普通用户';
        }
        
        // 更新欢迎标题
        const welcomeTitle = document.querySelector('.welcome-title');
        if (welcomeTitle) {
            welcomeTitle.textContent = `你好，${currentUser.nickname || currentUser.username || '探索者'}`;
        }
    }
}

// 设置用户界面
function setupUserInterface() {
    // 设置心象选择器
    setupMoodSelector();
    
    // 设置页面切换动画
    setupPageTransitions();
    
    // 设置卡片动画
    setupCardAnimations();
}

// 设置事件监听器
function setupEventListeners() {
    // 用户头像点击事件
    const avatar = document.getElementById('userAvatar');
    avatar.addEventListener('click', showUserProfile);
    
    // 页面滚动事件
    window.addEventListener('scroll', handleScroll);
    
    // 窗口大小变化事件
    window.addEventListener('resize', handleResize);
}

// API请求函数
async function apiRequest(endpoint, options = {}) {
    const url = `${API_BASE_URL}${endpoint}`;
    
    const defaultHeaders = {
        'Content-Type': 'application/json',
    };
    
    if (authToken) {
        defaultHeaders['Authorization'] = `Bearer ${authToken}`;
    }
    
    const config = {
        timeout: CONFIG.API_TIMEOUT,
        headers: defaultHeaders,
        ...options,
        headers: { ...defaultHeaders, ...options.headers }
    };
    
    try {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), CONFIG.API_TIMEOUT);
        
        const response = await fetch(url, {
            ...config,
            signal: controller.signal
        });
        
        clearTimeout(timeoutId);
        
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        const data = await response.json();
        return data;
        
    } catch (error) {
        console.error(`API请求失败 ${endpoint}:`, error);
        
        if (error.name === 'AbortError') {
            throw new Error('请求超时，请检查网络连接');
        }
        
        throw error;
    }
}

// 显示心象弹窗
function showMoodModal() {
    const lastCheckin = localStorage.getItem('lastMoodCheckin');
    const today = new Date().toDateString();
    
    if (lastCheckin !== today) {
        setTimeout(() => {
            const modal = document.getElementById('moodModal');
            modal.classList.add('show');
        }, 1500);
    } else {
        loadTodayMoodStatus();
    }
}

// 设置心象选择器
function setupMoodSelector() {
    const moodItems = document.querySelectorAll('.mood-item');
    
    moodItems.forEach(item => {
        item.addEventListener('click', () => {
            // 移除其他选中状态
            moodItems.forEach(i => i.classList.remove('selected'));
            
            // 添加当前选中状态
            item.classList.add('selected');
            selectedMood = item.getAttribute('data-mood');
            
            // 添加选中动画
            item.style.transform = 'translateY(-5px) scale(1.1)';
            setTimeout(() => {
                item.style.transform = '';
            }, 200);
        });
    });
}

// 提交心象签到
async function submitMoodCheckin() {
    if (!selectedMood) {
        showNotification('请选择你的心情状态', 'warning');
        return;
    }
    
    const moodData = {
        primary_mood: selectedMood,
        energy_level: 7,
        stress_level: 3,
        sleep_quality: 8,
        emotion_tags: [selectedMood],
        current_activity: 'reflection',
        environment: 'home',
        mood_intensity: 6,
        emotional_stability: 7,
        social_situation: '独处',
        physical_comfort: 8,
        health_status: '良好',
        focus_level: 7,
        anxiety_level: 2,
        confidence_level: 8,
        motivation_level: 7,
        daily_intentions: ['保持积极心态'],
        anticipated_challenges: [],
        desired_mood_state: 'positive'
    };
    
    try {
        // 调用后端API
        const response = await apiRequest('/api/daily-mood/checkin', {
            method: 'POST',
            body: JSON.stringify(moodData)
        });
        
        if (response.success) {
            // 保存本地记录
            localStorage.setItem('lastMoodCheckin', new Date().toDateString());
            localStorage.setItem('todayMood', JSON.stringify({
                mood: selectedMood,
                timestamp: new Date().toISOString(),
                score: response.mood_score || 7.5
            }));
            
            // 更新心情状态显示
            updateMoodStatus(selectedMood);
            
            // 更新连续签到天数
            updateStreakDays(response.streak_days || 1);
            
            // 关闭弹窗
            const modal = document.getElementById('moodModal');
            modal.classList.remove('show');
            
            // 显示成功消息
            showNotification('心象签到成功！✨', 'success');
            
            // 显示奖励动画
            showRewardAnimation();
        }
        
    } catch (error) {
        console.error('心象签到失败:', error);
        
        // 降级到本地存储
        localStorage.setItem('lastMoodCheckin', new Date().toDateString());
        localStorage.setItem('todayMood', JSON.stringify({
            mood: selectedMood,
            timestamp: new Date().toISOString()
        }));
        
        updateMoodStatus(selectedMood);
        
        const modal = document.getElementById('moodModal');
        modal.classList.remove('show');
        
        showNotification('心象记录已保存 💫', 'success');
    }
}

// 跳过心象签到
function skipMoodCheckin() {
    const modal = document.getElementById('moodModal');
    modal.classList.remove('show');
    showNotification('下次记得记录心情哦 🌟', 'info');
}

// 更新心情状态
function updateMoodStatus(mood) {
    const statusElement = document.getElementById('moodStatus');
    const moodLabels = {
        'excited': '🎉 兴奋满满',
        'happy': '😊 心情愉悦',
        'calm': '😌 内心平静',
        'focused': '🎯 专注状态',
        'tired': '😴 有些疲惫',
        'stressed': '😰 压力较大'
    };
    
    if (statusElement) {
        statusElement.textContent = moodLabels[mood] || '未记录';
        
        // 添加更新动画
        statusElement.style.transform = 'scale(1.1)';
        statusElement.style.transition = 'transform 0.3s ease';
        setTimeout(() => {
            statusElement.style.transform = 'scale(1)';
        }, 300);
    }
}

// 更新连续签到天数
function updateStreakDays(days) {
    const streakElement = document.getElementById('streakDays');
    if (streakElement) {
        streakElement.textContent = `${days}天`;
        
        // 添加庆祝动画
        if (days > 1) {
            streakElement.style.animation = 'bounce-tab 0.6s ease';
            setTimeout(() => {
                streakElement.style.animation = '';
            }, 600);
        }
    }
}

// 加载今日心情状态
function loadTodayMoodStatus() {
    const todayMood = localStorage.getItem('todayMood');
    if (todayMood) {
        const data = JSON.parse(todayMood);
        updateMoodStatus(data.mood);
    }
}

// 页面导航
function navigateToPage(pageId) {
    // 隐藏所有页面
    const pages = document.querySelectorAll('.page');
    pages.forEach(page => {
        page.classList.remove('active');
    });
    
    // 显示目标页面
    const targetPage = document.getElementById(pageId + 'Page');
    if (targetPage) {
        targetPage.classList.add('active');
        
        // 页面切换动画
        targetPage.style.opacity = '0';
        targetPage.style.transform = 'translateY(20px)';
        
        requestAnimationFrame(() => {
            targetPage.style.transition = 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)';
            targetPage.style.opacity = '1';
            targetPage.style.transform = 'translateY(0)';
        });
    }
    
    // 更新底部导航状态
    updateNavState(pageId);
    
    // 滚动到顶部
    window.scrollTo({ top: 0, behavior: 'smooth' });
}

// 更新导航状态
function updateNavState(activePageId) {
    const navTabs = document.querySelectorAll('.nav-tab');
    navTabs.forEach(tab => {
        tab.classList.remove('active');
        
        // 检查是否为当前激活的标签
        const tabText = tab.querySelector('.nav-label').textContent.toLowerCase();
        const pageMap = {
            '首页': 'home',
            '星象': 'astro', 
            '心理': 'psychology',
            '冥想': 'meditation',
            '我的': 'profile'
        };
        
        if (pageMap[tabText] === activePageId) {
            tab.classList.add('active');
        }
    });
}

// 显示出生星图
async function showBirthChart() {
    showNotification('正在生成专属星图...', 'info');
    
    try {
        const response = await apiRequest('/api/birth-chart/generate', {
            method: 'POST',
            body: JSON.stringify({
                birth_date: '1990-05-15',
                birth_time: '14:30',
                birth_location: '北京市'
            })
        });
        
        setTimeout(() => {
            showDetailModal('✨ 你的出生星图', `
                <div style="text-align: center; padding: 30px 20px;">
                    <div style="font-size: 64px; margin-bottom: 20px; background: linear-gradient(45deg, #f093fb, #4facfe); -webkit-background-clip: text; -webkit-text-fill-color: transparent; animation: glow 2s ease-in-out infinite alternate;">⭐</div>
                    <h3 style="color: #4a5568; margin-bottom: 20px;">专属星象解析</h3>
                    <div style="background: linear-gradient(135deg, #667eea, #764ba2); color: white; padding: 20px; border-radius: 15px; margin-bottom: 20px;">
                        <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 15px; text-align: center;">
                            <div>
                                <div style="font-size: 24px; margin-bottom: 5px;">☀️</div>
                                <div style="font-size: 12px; opacity: 0.8;">太阳</div>
                                <div style="font-weight: bold;">${currentUser.zodiac_sign || '金牛座'}</div>
                            </div>
                            <div>
                                <div style="font-size: 24px; margin-bottom: 5px;">🌙</div>
                                <div style="font-size: 12px; opacity: 0.8;">月亮</div>
                                <div style="font-weight: bold;">巨蟹座</div>
                            </div>
                            <div>
                                <div style="font-size: 24px; margin-bottom: 5px;">⬆️</div>
                                <div style="font-size: 12px; opacity: 0.8;">上升</div>
                                <div style="font-weight: bold;">处女座</div>
                            </div>
                        </div>
                    </div>
                    <div style="background: #f7fafc; padding: 20px; border-radius: 15px; text-align: left;">
                        <h4 style="color: #4a5568; margin-bottom: 15px;">🎯 核心特质</h4>
                        <ul style="color: #666; line-height: 1.8; list-style: none; padding: 0;">
                            <li>✨ 稳重务实，追求内心安全感</li>
                            <li>💪 执行力强，能将想法变为现实</li>
                            <li>🎨 对美有独特感知，艺术天赋突出</li>
                            <li>💝 重视情感连接，忠诚可靠</li>
                        </ul>
                    </div>
                </div>
            `);
        }, 1500);
        
    } catch (error) {
        console.error('星图生成失败:', error);
        // 显示模拟数据
        setTimeout(() => {
            showDetailModal('✨ 你的出生星图', '星图内容...');
        }, 1500);
    }
}

// 显示星象日历
async function showAstroCalendar() {
    try {
        const response = await apiRequest('/api/astro/calendar');
        
        showDetailModal('📅 星象日历', `
            <div style="padding: 25px;">
                <h3 style="margin-bottom: 20px; color: #4a5568; text-align: center;">本月重要星象</h3>
                
                <div style="background: linear-gradient(135deg, #fff3cd, #ffeaa7); padding: 20px; border-radius: 15px; margin-bottom: 15px; border-left: 4px solid #f39c12;">
                    <div style="display: flex; align-items: center; gap: 10px; margin-bottom: 8px;">
                        <span style="font-size: 24px;">🌙</span>
                        <strong style="color: #d68910;">7月15日 - 新月在巨蟹座</strong>
                    </div>
                    <p style="color: #8b6914; margin: 0; font-size: 14px;">适合开始新的情感计划，家庭关系和内心成长的绝佳时机</p>
                </div>
                
                <div style="background: linear-gradient(135deg, #f8d7da, #fab1a0); padding: 20px; border-radius: 15px; margin-bottom: 15px; border-left: 4px solid #e74c3c;">
                    <div style="display: flex; align-items: center; gap: 10px; margin-bottom: 8px;">
                        <span style="font-size: 24px;">⚠️</span>
                        <strong style="color: #c0392b;">7月18日 - 水星逆行开始</strong>
                    </div>
                    <p style="color: #a93226; margin: 0; font-size: 14px;">注意沟通误解和电子设备故障，重要决定建议延后</p>
                </div>
                
                <div style="background: linear-gradient(135deg, #d1ecf1, #81ecec); padding: 20px; border-radius: 15px; border-left: 4px solid #00b894;">
                    <div style="display: flex; align-items: center; gap: 10px; margin-bottom: 8px;">
                        <span style="font-size: 24px;">🌕</span>
                        <strong style="color: #00a085;">7月31日 - 满月在水瓶座</strong>
                    </div>
                    <p style="color: #008977; margin: 0; font-size: 14px;">情绪能量达到高峰，适合释放和突破创新</p>
                </div>
                
                <div style="text-align: center; margin-top: 25px;">
                    <button onclick="setAstroReminder()" style="background: linear-gradient(135deg, #667eea, #764ba2); color: white; border: none; padding: 12px 24px; border-radius: 20px; font-weight: 600; cursor: pointer;">
                        🔔 设置星象提醒
                    </button>
                </div>
            </div>
        `);
        
    } catch (error) {
        console.error('星象日历加载失败:', error);
        showNotification('星象日历暂时无法加载', 'error');
    }
}

// 显示原生家庭分析
async function showFamilyAnalysis() {
    showNotification('正在分析情感模式...', 'info');
    
    try {
        const response = await apiRequest('/api/psychology/family-analysis', {
            method: 'POST'
        });
        
        setTimeout(() => {
            showDetailModal('💝 情感依恋分析', `
                <div style="padding: 25px;">
                    <div style="text-align: center; margin-bottom: 25px;">
                        <div style="font-size: 48px; margin-bottom: 15px;">💝</div>
                        <h3 style="color: #4a5568;">你的情感依恋模式</h3>
                    </div>
                    
                    <div style="background: linear-gradient(135deg, #e7f3ff, #74b9ff); padding: 25px; border-radius: 20px; margin-bottom: 20px; color: #2d3748;">
                        <div style="text-align: center; margin-bottom: 15px;">
                            <div style="font-size: 36px; margin-bottom: 10px;">🌟</div>
                            <strong style="font-size: 20px; color: #0984e3;">安全型依恋 - 85%</strong>
                        </div>
                        <p style="margin: 0; text-align: center; font-size: 14px; color: #2d3748;">在关系中表现稳定，能够健康地给予和接受爱</p>
                    </div>
                    
                    <div style="background: #f8f9fa; padding: 20px; border-radius: 15px; margin-bottom: 20px;">
                        <h4 style="margin-bottom: 15px; color: #4a5568;">✨ 你的优势特质</h4>
                        <div style="display: grid; gap: 10px;">
                            <div style="display: flex; align-items: center; gap: 10px;">
                                <span style="color: #00b894;">✓</span>
                                <span style="color: #2d3748;">情感表达自然流畅</span>
                            </div>
                            <div style="display: flex; align-items: center; gap: 10px;">
                                <span style="color: #00b894;">✓</span>
                                <span style="color: #2d3748;">信任他人的能力强</span>
                            </div>
                            <div style="display: flex; align-items: center; gap: 10px;">
                                <span style="color: #00b894;">✓</span>
                                <span style="color: #2d3748;">冲突处理成熟理性</span>
                            </div>
                        </div>
                    </div>
                    
                    <div style="background: #fff5f5; padding: 20px; border-radius: 15px;">
                        <h4 style="margin-bottom: 15px; color: #4a5568;">🎯 成长建议</h4>
                        <ul style="color: #666; line-height: 1.8; margin: 0; padding-left: 20px;">
                            <li>继续发挥情感稳定的天然优势</li>
                            <li>在关系中适当表达个人需求</li>
                            <li>保持健康的边界感和独立性</li>
                            <li>帮助伴侣建立更安全的依恋</li>
                        </ul>
                    </div>
                </div>
            `);
        }, 1500);
        
    } catch (error) {
        console.error('家庭分析失败:', error);
        showNotification('分析功能暂时不可用', 'error');
    }
}

// 显示呼吸训练
function showBreathing() {
    showDetailModal('💨 呼吸训练', `
        <div style="text-align: center; padding: 30px 20px;">
            <div style="font-size: 64px; margin-bottom: 20px; background: linear-gradient(45deg, #00b894, #74b9ff); -webkit-background-clip: text; -webkit-text-fill-color: transparent;">💨</div>
            <h3 style="color: #4a5568; margin-bottom: 20px;">4-7-8 深度放松呼吸法</h3>
            
            <div style="background: linear-gradient(135deg, #f7fafc, #e2e8f0); padding: 25px; border-radius: 20px; margin: 25px 0;">
                <div style="font-size: 20px; margin-bottom: 15px; color: #4a5568; font-weight: 600;">准备开始</div>
                <div style="color: #666; line-height: 1.8; margin-bottom: 20px;">
                    <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 15px; text-align: center;">
                        <div style="background: white; padding: 15px; border-radius: 10px;">
                            <div style="font-size: 24px; margin-bottom: 5px; color: #00b894;">1️⃣</div>
                            <div style="font-size: 12px; color: #666;">吸气 4 秒</div>
                        </div>
                        <div style="background: white; padding: 15px; border-radius: 10px;">
                            <div style="font-size: 24px; margin-bottom: 5px; color: #fdcb6e;">2️⃣</div>
                            <div style="font-size: 12px; color: #666;">屏气 7 秒</div>
                        </div>
                        <div style="background: white; padding: 15px; border-radius: 10px;">
                            <div style="font-size: 24px; margin-bottom: 5px; color: #74b9ff;">3️⃣</div>
                            <div style="font-size: 12px; color: #666;">呼气 8 秒</div>
                        </div>
                    </div>
                </div>
                <p style="color: #666; font-size: 14px; margin: 0;">重复 4 次循环，帮助激活副交感神经系统</p>
            </div>
            
            <div style="display: flex; gap: 15px; justify-content: center;">
                <button onclick="closeDetailModal()" style="background: transparent; border: 2px solid #ddd; color: #666; padding: 12px 24px; border-radius: 20px; cursor: pointer;">
                    稍后练习
                </button>
                <button onclick="startBreathingExercise()" style="background: linear-gradient(135deg, #00b894, #74b9ff); color: white; border: none; padding: 12px 24px; border-radius: 20px; font-weight: 600; cursor: pointer;">
                    立即开始
                </button>
            </div>
        </div>
    `);
}

// 显示情绪日记
function showEmotionDiary() {
    showDetailModal('📝 情绪日记', `
        <div style="padding: 25px;">
            <div style="text-align: center; margin-bottom: 25px;">
                <div style="font-size: 48px; margin-bottom: 15px;">📝</div>
                <h3 style="color: #4a5568;">记录此刻的内心世界</h3>
            </div>
            
            <div style="margin-bottom: 20px;">
                <label style="display: block; margin-bottom: 10px; font-weight: 600; color: #4a5568;">💭 此刻你在想什么？</label>
                <textarea 
                    id="emotionText"
                    placeholder="分享你的感受、想法，或今天发生的事情..." 
                    style="width: 100%; height: 120px; padding: 15px; border: 2px solid #e2e8f0; border-radius: 15px; resize: vertical; font-family: inherit; font-size: 14px; line-height: 1.6;"
                ></textarea>
            </div>
            
            <div style="margin-bottom: 20px;">
                <label style="display: block; margin-bottom: 10px; font-weight: 600; color: #4a5568;">🌡️ 情绪强度</label>
                <div style="display: flex; align-items: center; gap: 15px;">
                    <span style="font-size: 12px; color: #666;">平静</span>
                    <input 
                        type="range" 
                        id="emotionIntensity"
                        min="1" 
                        max="10" 
                        value="5" 
                        style="flex: 1; height: 6px; border-radius: 3px; background: #e2e8f0; outline: none;"
                    >
                    <span style="font-size: 12px; color: #666;">强烈</span>
                    <span id="intensityValue" style="background: #667eea; color: white; padding: 4px 8px; border-radius: 10px; font-size: 12px; font-weight: bold;">5</span>
                </div>
            </div>
            
            <div style="margin-bottom: 25px;">
                <label style="display: block; margin-bottom: 10px; font-weight: 600; color: #4a5568;">🏷️ 情绪标签</label>
                <div style="display: flex; flex-wrap: wrap; gap: 8px;">
                    ${['快乐', '平静', '感激', '兴奋', '紧张', '疲惫', '焦虑', '愤怒', '悲伤', '孤独'].map(tag => 
                        `<span onclick="toggleEmotionTag(this)" data-tag="${tag}" style="background: #f7fafc; border: 1px solid #e2e8f0; padding: 6px 12px; border-radius: 15px; font-size: 12px; cursor: pointer; transition: all 0.3s ease;">${tag}</span>`
                    ).join('')}
                </div>
            </div>
            
            <button onclick="saveEmotionDiary()" style="background: linear-gradient(135deg, #667eea, #764ba2); color: white; border: none; padding: 15px 30px; border-radius: 20px; font-size: 16px; font-weight: 600; cursor: pointer; width: 100%;">
                💾 保存情绪记录
            </button>
        </div>
    `);
    
    // 设置强度滑块事件
    const intensitySlider = document.getElementById('emotionIntensity');
    const intensityValue = document.getElementById('intensityValue');
    
    intensitySlider.addEventListener('input', () => {
        intensityValue.textContent = intensitySlider.value;
    });
}

// 切换情绪标签
function toggleEmotionTag(element) {
    element.classList.toggle('selected');
    if (element.classList.contains('selected')) {
        element.style.background = 'linear-gradient(135deg, #667eea, #764ba2)';
        element.style.color = 'white';
        element.style.borderColor = 'transparent';
    } else {
        element.style.background = '#f7fafc';
        element.style.color = '#4a5568';
        element.style.borderColor = '#e2e8f0';
    }
}

// 保存情绪日记
async function saveEmotionDiary() {
    const text = document.getElementById('emotionText').value;
    const intensity = document.getElementById('emotionIntensity').value;
    const selectedTags = Array.from(document.querySelectorAll('[data-tag].selected')).map(el => el.dataset.tag);
    
    if (!text.trim()) {
        showNotification('请先写下你的感受', 'warning');
        return;
    }
    
    const emotionData = {
        content: text,
        intensity: parseInt(intensity),
        emotion_tags: selectedTags,
        timestamp: new Date().toISOString()
    };
    
    try {
        const response = await apiRequest('/api/emotion-diary/create', {
            method: 'POST',
            body: JSON.stringify(emotionData)
        });
        
        if (response.success) {
            showNotification('情绪记录已保存 ✨', 'success');
            closeDetailModal();
            
            // 显示AI分析结果
            setTimeout(() => {
                showEmotionAnalysis(response.ai_insights);
            }, 500);
        }
        
    } catch (error) {
        console.error('情绪日记保存失败:', error);
        
        // 降级到本地存储
        const diaryEntries = JSON.parse(localStorage.getItem('emotionDiary') || '[]');
        diaryEntries.push(emotionData);
        localStorage.setItem('emotionDiary', JSON.stringify(diaryEntries));
        
        showNotification('记录已本地保存 💫', 'success');
        closeDetailModal();
    }
}

// 显示情绪分析
function showEmotionAnalysis(insights) {
    showDetailModal('🤖 AI情绪洞察', `
        <div style="padding: 25px; text-align: center;">
            <div style="font-size: 48px; margin-bottom: 20px;">🤖</div>
            <h3 style="color: #4a5568; margin-bottom: 25px;">专属情绪分析报告</h3>
            
            <div style="background: linear-gradient(135deg, #667eea, #764ba2); color: white; padding: 25px; border-radius: 20px; margin-bottom: 20px;">
                <h4 style="margin-bottom: 15px;">💡 AI洞察</h4>
                <p style="margin: 0; line-height: 1.6;">
                    ${insights || '从你的记录中，我感受到你正在积极面对内心的变化。保持这样的自我觉察很重要，它是成长的第一步。'}
                </p>
            </div>
            
            <div style="background: #f8f9fa; padding: 20px; border-radius: 15px; margin-bottom: 20px;">
                <h4 style="color: #4a5568; margin-bottom: 15px;">📈 情绪模式趋势</h4>
                <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 15px; text-align: center;">
                    <div>
                        <div style="font-size: 24px; margin-bottom: 5px;">📊</div>
                        <div style="font-size: 12px; color: #666;">整体稳定性</div>
                        <div style="font-weight: bold; color: #00b894;">良好</div>
                    </div>
                    <div>
                        <div style="font-size: 24px; margin-bottom: 5px;">🎯</div>
                        <div style="font-size: 12px; color: #666;">自我觉察</div>
                        <div style="font-weight: bold; color: #0984e3;">优秀</div>
                    </div>
                    <div>
                        <div style="font-size: 24px; margin-bottom: 5px;">💪</div>
                        <div style="font-size: 12px; color: #666;">适应能力</div>
                        <div style="font-weight: bold; color: #fdcb6e;">提升中</div>
                    </div>
                </div>
            </div>
            
            <button onclick="closeDetailModal()" style="background: linear-gradient(135deg, #667eea, #764ba2); color: white; border: none; padding: 12px 24px; border-radius: 20px; font-weight: 600; cursor: pointer;">
                继续探索
            </button>
        </div>
    `);
}

// 开始呼吸练习
function startBreathingExercise() {
    closeDetailModal();
    
    const modal = createModal();
    modal.innerHTML = `
        <div style="text-align: center; padding: 50px 30px; background: rgba(0, 0, 0, 0.8); border-radius: 30px; backdrop-filter: blur(20px);">
            <div id="breathingCircle" style="
                width: 200px; 
                height: 200px; 
                border: 4px solid #74b9ff; 
                border-radius: 50%; 
                margin: 0 auto 40px; 
                display: flex; 
                align-items: center; 
                justify-content: center; 
                font-size: 32px; 
                font-weight: bold; 
                color: #74b9ff; 
                transition: all 4s cubic-bezier(0.4, 0, 0.2, 1);
                background: rgba(116, 185, 255, 0.1);
                box-shadow: 0 0 30px rgba(116, 185, 255, 0.3);
            ">
                准备
            </div>
            <div id="breathingInstruction" style="font-size: 24px; margin-bottom: 20px; color: white; font-weight: 600;">
                深呼吸，放松身心
            </div>
            <div id="breathingCounter" style="font-size: 18px; color: rgba(255, 255, 255, 0.7); margin-bottom: 40px;">
                第 1 / 4 轮
            </div>
            <button onclick="closeModal()" style="background: rgba(255, 255, 255, 0.2); color: white; border: 1px solid rgba(255, 255, 255, 0.3); padding: 12px 24px; border-radius: 20px; cursor: pointer; backdrop-filter: blur(10px);">
                结束练习
            </button>
        </div>
    `;
    
    startBreathingCycle();
}

// 开始呼吸循环
function startBreathingCycle() {
    let cycle = 1;
    const maxCycles = 4;
    
    function runCycle() {
        if (cycle > maxCycles) {
            const instruction = document.getElementById('breathingInstruction');
            const circle = document.getElementById('breathingCircle');
            
            instruction.textContent = '练习完成！感受内心的平静';
            circle.textContent = '✓';
            circle.style.borderColor = '#00b894';
            circle.style.color = '#00b894';
            circle.style.background = 'rgba(0, 184, 148, 0.2)';
            circle.style.boxShadow = '0 0 40px rgba(0, 184, 148, 0.5)';
            
            showNotification('呼吸练习完成，感受到平静了吗？🌸', 'success');
            return;
        }
        
        const counter = document.getElementById('breathingCounter');
        const instruction = document.getElementById('breathingInstruction');
        const circle = document.getElementById('breathingCircle');
        
        counter.textContent = `第 ${cycle} / ${maxCycles} 轮`;
        
        // 吸气 4秒
        instruction.textContent = '慢慢吸气...';
        circle.style.transform = 'scale(1.4)';
        circle.style.background = 'rgba(116, 185, 255, 0.3)';
        circle.style.boxShadow = '0 0 50px rgba(116, 185, 255, 0.6)';
        
        setTimeout(() => {
            // 屏气 7秒
            instruction.textContent = '保持呼吸...';
            circle.style.borderColor = '#fdcb6e';
            circle.style.color = '#fdcb6e';
            circle.style.background = 'rgba(253, 203, 110, 0.3)';
            circle.style.boxShadow = '0 0 50px rgba(253, 203, 110, 0.6)';
            
            setTimeout(() => {
                // 呼气 8秒
                instruction.textContent = '缓缓呼气...';
                circle.style.transform = 'scale(1)';
                circle.style.borderColor = '#00b894';
                circle.style.color = '#00b894';
                circle.style.background = 'rgba(0, 184, 148, 0.2)';
                circle.style.boxShadow = '0 0 40px rgba(0, 184, 148, 0.4)';
                
                setTimeout(() => {
                    cycle++;
                    // 重置颜色为下一轮
                    circle.style.borderColor = '#74b9ff';
                    circle.style.color = '#74b9ff';
                    runCycle();
                }, 8000);
            }, 7000);
        }, 4000);
    }
    
    setTimeout(runCycle, 1000);
}

// 设置星象提醒
function setAstroReminder() {
    showNotification('星象提醒已设置 🔔', 'success');
    closeDetailModal();
}

// 加载用户数据
async function loadUserData() {
    try {
        // 加载用户偏好设置
        const preferences = await apiRequest('/api/user/preferences');
        if (preferences) {
            applyUserPreferences(preferences);
        }
        
        // 加载今日数据概览
        const todayData = await apiRequest('/api/user/today-summary');
        if (todayData) {
            updateTodaySummary(todayData);
        }
        
    } catch (error) {
        console.error('用户数据加载失败:', error);
        // 使用本地缓存数据
        loadLocalData();
    }
}

// 应用用户偏好
function applyUserPreferences(preferences) {
    // 应用主题设置
    if (preferences.theme) {
        document.body.className = preferences.theme;
    }
    
    // 应用动画设置
    if (preferences.reduce_motion) {
        document.body.style.setProperty('--animation-duration', '0s');
    }
}

// 更新今日数据概览
function updateTodaySummary(data) {
    // 更新连续签到天数
    if (data.streak_days) {
        updateStreakDays(data.streak_days);
    }
    
    // 更新今日运势
    if (data.daily_fortune) {
        updateDailyFortune(data.daily_fortune);
    }
}

// 加载本地数据
function loadLocalData() {
    loadTodayMoodStatus();
    
    const streakDays = localStorage.getItem('streakDays') || '1';
    updateStreakDays(parseInt(streakDays));
}

// 设置页面切换动画
function setupPageTransitions() {
    // 页面切换时的渐变效果
    const style = document.createElement('style');
    style.textContent = `
        .page {
            opacity: 0;
            transform: translateY(20px);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        }
        .page.active {
            opacity: 1;
            transform: translateY(0);
        }
    `;
    document.head.appendChild(style);
}

// 设置卡片动画
function setupCardAnimations() {
    const observer = new IntersectionObserver((entries) => {
        entries.forEach((entry, index) => {
            if (entry.isIntersecting) {
                setTimeout(() => {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }, index * 100);
            }
        });
    }, { threshold: 0.1 });
    
    const cards = document.querySelectorAll('.feature-card');
    cards.forEach(card => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(30px)';
        card.style.transition = 'all 0.6s cubic-bezier(0.4, 0, 0.2, 1)';
        observer.observe(card);
    });
}

// 显示用户资料
function showUserProfile() {
    showDetailModal('👤 个人资料', `
        <div style="padding: 25px; text-align: center;">
            <div style="width: 80px; height: 80px; border-radius: 50%; background: linear-gradient(135deg, #667eea, #764ba2); display: flex; align-items: center; justify-content: center; color: white; font-size: 32px; font-weight: bold; margin: 0 auto 20px;">
                ${(currentUser.nickname || currentUser.username || 'U')[0].toUpperCase()}
            </div>
            
            <h3 style="color: #4a5568; margin-bottom: 10px;">${currentUser.nickname || currentUser.username || '探索者'}</h3>
            <div style="background: linear-gradient(135deg, #fa709a, #fee140); color: white; padding: 4px 12px; border-radius: 15px; font-size: 12px; font-weight: 600; display: inline-block; margin-bottom: 25px;">
                ${document.getElementById('userLevel').textContent}
            </div>
            
            <div style="background: #f8f9fa; padding: 20px; border-radius: 15px; margin-bottom: 20px;">
                <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 15px; text-align: center;">
                    <div>
                        <div style="font-size: 24px; margin-bottom: 5px; color: #667eea;">📊</div>
                        <div style="font-size: 12px; color: #666;">使用天数</div>
                        <div style="font-weight: bold; color: #4a5568;">7天</div>
                    </div>
                    <div>
                        <div style="font-size: 24px; margin-bottom: 5px; color: #00b894;">🎯</div>
                        <div style="font-size: 12px; color: #666;">完成测试</div>
                        <div style="font-weight: bold; color: #4a5568;">3个</div>
                    </div>
                    <div>
                        <div style="font-size: 24px; margin-bottom: 5px; color: #fdcb6e;">🧘‍♀️</div>
                        <div style="font-size: 12px; color: #666;">冥想时长</div>
                        <div style="font-weight: bold; color: #4a5568;">45分钟</div>
                    </div>
                    <div>
                        <div style="font-size: 24px; margin-bottom: 5px; color: #e17055;">📝</div>
                        <div style="font-size: 12px; color: #666;">日记条数</div>
                        <div style="font-weight: bold; color: #4a5568;">12条</div>
                    </div>
                </div>
            </div>
            
            <button onclick="logout()" style="background: transparent; border: 2px solid #ddd; color: #666; padding: 10px 20px; border-radius: 15px; cursor: pointer; width: 100%;">
                退出登录
            </button>
        </div>
    `);
}

// 退出登录
async function logout() {
    try {
        if (authToken) {
            await apiRequest('/api/auth/logout', { method: 'POST' });
        }
    } catch (error) {
        console.log('退出登录请求失败:', error);
    }
    
    // 清除本地数据
    localStorage.removeItem('authToken');
    localStorage.removeItem('userInfo');
    authToken = null;
    currentUser = null;
    
    closeDetailModal();
    showNotification('已成功退出登录', 'success');
    
    // 重新初始化应用
    setTimeout(() => {
        window.location.reload();
    }, 1000);
}

// 处理滚动事件
function handleScroll() {
    const nav = document.querySelector('.top-nav');
    if (window.scrollY > 50) {
        nav.style.background = 'rgba(255, 255, 255, 0.95)';
        nav.style.backdropFilter = 'blur(30px)';
    } else {
        nav.style.background = 'rgba(255, 255, 255, 0.1)';
        nav.style.backdropFilter = 'blur(20px)';
    }
}

// 处理窗口大小变化
function handleResize() {
    // 重新计算粒子位置
    const particles = document.querySelectorAll('.particle');
    particles.forEach(particle => {
        particle.style.left = Math.random() * 100 + '%';
    });
}

// 显示奖励动画
function showRewardAnimation() {
    const reward = document.createElement('div');
    reward.innerHTML = '🎉';
    reward.style.cssText = `
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        font-size: 64px;
        z-index: 5000;
        animation: reward-bounce 1s ease-out forwards;
        pointer-events: none;
    `;
    
    const style = document.createElement('style');
    style.textContent = `
        @keyframes reward-bounce {
            0% { transform: translate(-50%, -50%) scale(0); opacity: 0; }
            50% { transform: translate(-50%, -50%) scale(1.2); opacity: 1; }
            100% { transform: translate(-50%, -50%) scale(1) translateY(-100px); opacity: 0; }
        }
    `;
    document.head.appendChild(style);
    document.body.appendChild(reward);
    
    setTimeout(() => {
        reward.remove();
        style.remove();
    }, 1000);
}

// 显示加载覆盖层
function showLoadingOverlay() {
    const overlay = document.createElement('div');
    overlay.id = 'loadingOverlay';
    overlay.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 9999;
        flex-direction: column;
        color: white;
    `;
    
    overlay.innerHTML = `
        <div style="text-align: center;">
            <div style="font-size: 48px; margin-bottom: 20px; animation: sparkle 2s ease-in-out infinite;">✨</div>
            <div style="font-size: 24px; font-weight: 800; margin-bottom: 10px;">数字星辰</div>
            <div style="font-size: 16px; opacity: 0.9;">正在为你准备专属体验...</div>
            <div class="loading-spinner" style="margin-top: 30px;"></div>
        </div>
    `;
    
    document.body.appendChild(overlay);
}

// 隐藏加载覆盖层
function hideLoadingOverlay() {
    const overlay = document.getElementById('loadingOverlay');
    if (overlay) {
        overlay.style.opacity = '0';
        overlay.style.transition = 'opacity 0.5s ease';
        setTimeout(() => overlay.remove(), 500);
    }
}

// 显示详情模态框
function showDetailModal(title, content) {
    const modal = createModal();
    modal.innerHTML = `
        <div style="background: white; margin: 20px; border-radius: 25px; max-width: 500px; width: 100%; max-height: 85vh; overflow-y: auto; box-shadow: 0 20px 60px rgba(31, 38, 135, 0.5);">
            <div style="padding: 25px 25px 15px 25px; border-bottom: 1px solid #f1f5f9; display: flex; justify-content: space-between; align-items: center;">
                <h2 style="margin: 0; color: #4a5568; font-size: 20px; font-weight: 800;">${title}</h2>
                <button onclick="closeDetailModal()" style="background: none; border: none; font-size: 28px; cursor: pointer; color: #cbd5e0; padding: 0; line-height: 1; transition: color 0.3s ease;">×</button>
            </div>
            <div>${content}</div>
        </div>
    `;
}

// 创建模态框
function createModal() {
    const modal = document.createElement('div');
    modal.id = 'detailModal';
    modal.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.7);
        backdrop-filter: blur(10px);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 3000;
        animation: modalFadeIn 0.3s ease;
    `;
    
    document.body.appendChild(modal);
    return modal;
}

// 关闭详情模态框
function closeDetailModal() {
    const modal = document.getElementById('detailModal');
    if (modal) {
        modal.style.animation = 'modalFadeOut 0.3s ease forwards';
        setTimeout(() => modal.remove(), 300);
    }
}

// 关闭模态框 (通用)
function closeModal() {
    closeDetailModal();
}

// 显示通知
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    const colors = {
        success: 'linear-gradient(135deg, #00b894, #00a085)',
        info: 'linear-gradient(135deg, #74b9ff, #0984e3)',
        warning: 'linear-gradient(135deg, #fdcb6e, #e17055)',
        error: 'linear-gradient(135deg, #fd79a8, #e84393)'
    };
    
    const icons = {
        success: '✨',
        info: '💫',
        warning: '⚠️',
        error: '❌'
    };
    
    notification.style.cssText = `
        position: fixed;
        top: 100px;
        left: 50%;
        transform: translateX(-50%);
        background: ${colors[type]};
        color: white;
        padding: 15px 25px;
        border-radius: 25px;
        font-weight: 600;
        z-index: 4000;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        animation: notificationSlideIn 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        display: flex;
        align-items: center;
        gap: 10px;
        backdrop-filter: blur(20px);
        max-width: 90%;
        text-align: center;
    `;
    
    notification.innerHTML = `
        <span style="font-size: 18px;">${icons[type]}</span>
        <span>${message}</span>
    `;
    
    document.body.appendChild(notification);
    
    // 3.5秒后自动消失
    setTimeout(() => {
        notification.style.animation = 'notificationSlideOut 0.4s cubic-bezier(0.4, 0, 0.2, 1) forwards';
        setTimeout(() => notification.remove(), 400);
    }, 3500);
}

// 添加动画CSS
const style = document.createElement('style');
style.textContent = `
    @keyframes notificationSlideIn {
        from { opacity: 0; transform: translateX(-50%) translateY(-20px) scale(0.9); }
        to { opacity: 1; transform: translateX(-50%) translateY(0) scale(1); }
    }
    @keyframes notificationSlideOut {
        from { opacity: 1; transform: translateX(-50%) translateY(0) scale(1); }
        to { opacity: 0; transform: translateX(-50%) translateY(-20px) scale(0.9); }
    }
    @keyframes modalFadeIn {
        from { opacity: 0; }
        to { opacity: 1; }
    }
    @keyframes modalFadeOut {
        from { opacity: 1; }
        to { opacity: 0; }
    }
    .feature-card:nth-child(1) { animation-delay: 0.1s; }
    .feature-card:nth-child(2) { animation-delay: 0.2s; }
    .feature-card:nth-child(3) { animation-delay: 0.3s; }
    .feature-card:nth-child(4) { animation-delay: 0.4s; }
    .feature-card:nth-child(5) { animation-delay: 0.5s; }
    .feature-card:nth-child(6) { animation-delay: 0.6s; }
`;
document.head.appendChild(style);