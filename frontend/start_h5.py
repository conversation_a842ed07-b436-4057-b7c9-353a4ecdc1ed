#!/usr/bin/env python3
"""
H5测试客户端启动脚本
运行方式: python start_h5.py
"""

import http.server
import socketserver
import webbrowser
import os
import sys
from pathlib import Path

# 设置端口
PORT = 3000
HOST = "localhost"

# 获取当前脚本目录
current_dir = Path(__file__).parent
frontend_dir = current_dir

class CORSRequestHandler(http.server.SimpleHTTPRequestHandler):
    """支持CORS的HTTP请求处理器"""
    
    def end_headers(self):
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type, Authorization')
        super().end_headers()
    
    def do_OPTIONS(self):
        self.send_response(200)
        self.end_headers()

def start_server():
    """启动H5测试服务器"""
    
    # 切换到前端目录
    os.chdir(frontend_dir)
    
    # 检查index.html是否存在
    if not Path('index.html').exists():
        print("❌ 错误: 找不到 index.html 文件")
        print(f"当前目录: {frontend_dir}")
        return False
    
    try:
        # 创建服务器
        with socketserver.TCPServer((HOST, PORT), CORSRequestHandler) as httpd:
            print("🚀 H5测试客户端启动中...")
            print(f"📁 服务目录: {frontend_dir}")
            print(f"🌐 访问地址: http://{HOST}:{PORT}")
            print(f"📱 移动端访问: http://localhost:{PORT}")
            print("\n" + "="*50)
            print("💡 功能说明:")
            print("• 👤 用户认证 - 登录/注册/用户信息管理")
            print("• 💭 今日心象 - 情绪状态记录和分析") 
            print("• ⭐ 星象日历 - 星座运势和天体事件")
            print("• 🧠 心理测试 - MBTI/九型人格/大五人格")
            print("• 🧘‍♀️ 冥想调节 - 冥想推荐和呼吸训练")
            print("• 🤖 AI分析 - 综合命理分析和洞察")
            print("="*50)
            print("\n📋 测试账号:")
            print("• 用户名: testuser1, 密码: password123 (金卡VIP)")
            print("• 用户名: admin, 密码: admin123456 (白金VIP)")
            print("\n⚡ 后端API: http://localhost:8000")
            print("📖 API文档: http://localhost:8000/docs")
            print("\n按 Ctrl+C 停止服务器")
            print("-"*50)
            
            # 自动打开浏览器
            try:
                webbrowser.open(f'http://{HOST}:{PORT}')
                print(f"🌍 已自动打开浏览器: http://{HOST}:{PORT}")
            except:
                print("❗ 无法自动打开浏览器，请手动访问上述地址")
            
            # 启动服务器
            httpd.serve_forever()
            
    except OSError as e:
        if e.errno == 48:  # Address already in use
            print(f"❌ 端口 {PORT} 已被占用")
            print(f"请尝试关闭占用端口的程序，或修改PORT变量使用其他端口")
            return False
        else:
            print(f"❌ 服务器启动失败: {e}")
            return False
    except KeyboardInterrupt:
        print("\n\n⏹️  服务器已停止")
        return True

if __name__ == "__main__":
    print("🔮 数字命理 H5 测试客户端")
    print("="*50)
    
    success = start_server()
    if success:
        print("✅ 服务器已正常关闭")
    else:
        print("❌ 服务器启动失败")
        sys.exit(1)