import React, { useState } from 'react'
import { View, Text, Input, Button, Form } from '@tarojs/components'
import { authApi } from '../../services/api'
import { useUserStore } from '../../store/user-store'
import Taro from '@tarojs/taro'
import './index.scss'

const Register: React.FC = () => {
  const [formData, setFormData] = useState({
    username: '',
    password: '',
    confirmPassword: '',
    phone: '',
    email: ''
  })
  const [loading, setLoading] = useState(false)
  const { setUser } = useUserStore()

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const validateForm = () => {
    if (!formData.username.trim()) {
      Taro.showToast({ title: '请输入用户名', icon: 'none' })
      return false
    }
    if (formData.username.length < 3) {
      Taro.showToast({ title: '用户名至少3个字符', icon: 'none' })
      return false
    }
    if (!formData.password) {
      Taro.showToast({ title: '请输入密码', icon: 'none' })
      return false
    }
    if (formData.password.length < 6) {
      Taro.showToast({ title: '密码至少6个字符', icon: 'none' })
      return false
    }
    if (formData.password !== formData.confirmPassword) {
      Taro.showToast({ title: '两次密码不一致', icon: 'none' })
      return false
    }
    return true
  }

  const handleRegister = async () => {
    if (!validateForm()) return

    setLoading(true)
    try {
      const response = await authApi.register({
        username: formData.username,
        password: formData.password,
        phone: formData.phone || undefined,
        email: formData.email || undefined
      })

      if (response.code === 200) {
        Taro.showToast({ title: '注册成功', icon: 'success' })
        
        // 注册成功后自动登录
        const loginResponse = await authApi.login({
          username: formData.username,
          password: formData.password
        })
        
        if (loginResponse.code === 200) {
          setUser({
            ...loginResponse.data.user,
            isLoggedIn: true
          })
          localStorage.setItem('access_token', loginResponse.data.access_token)
          
          // 返回首页
          Taro.navigateBack()
        }
      }
    } catch (error: any) {
      console.error('注册失败:', error)
      Taro.showToast({ 
        title: error.message || '注册失败', 
        icon: 'none' 
      })
    } finally {
      setLoading(false)
    }
  }

  const handleBackToLogin = () => {
    Taro.navigateBack()
  }

  return (
    <View className='register-page'>
      <View className='header'>
        <Text className='title'>创建账户</Text>
        <Text className='subtitle'>加入Numerology社区</Text>
      </View>

      <View className='form-container'>
        <View className='form-item'>
          <Text className='label'>用户名 *</Text>
          <Input
            className='input'
            value={formData.username}
            placeholder='请输入用户名'
            onInput={(e) => handleInputChange('username', e.detail.value)}
          />
        </View>

        <View className='form-item'>
          <Text className='label'>密码 *</Text>
          <Input
            className='input'
            type='password'
            value={formData.password}
            placeholder='请输入密码(至少6位)'
            onInput={(e) => handleInputChange('password', e.detail.value)}
          />
        </View>

        <View className='form-item'>
          <Text className='label'>确认密码 *</Text>
          <Input
            className='input'
            type='password'
            value={formData.confirmPassword}
            placeholder='请再次输入密码'
            onInput={(e) => handleInputChange('confirmPassword', e.detail.value)}
          />
        </View>

        <View className='form-item'>
          <Text className='label'>手机号</Text>
          <Input
            className='input'
            type='number'
            value={formData.phone}
            placeholder='请输入手机号(可选)'
            onInput={(e) => handleInputChange('phone', e.detail.value)}
          />
        </View>

        <View className='form-item'>
          <Text className='label'>邮箱</Text>
          <Input
            className='input'
            type='email'
            value={formData.email}
            placeholder='请输入邮箱(可选)'
            onInput={(e) => handleInputChange('email', e.detail.value)}
          />
        </View>

        <Button
          className='register-btn'
          type='primary'
          loading={loading}
          onClick={handleRegister}
        >
          {loading ? '注册中...' : '立即注册'}
        </Button>

        <View className='login-link'>
          <Text onClick={handleBackToLogin} className='link-text'>
            已有账户？返回登录
          </Text>
        </View>
      </View>

      <View className='tips'>
        <Text className='tip-text'>🔐 您的隐私和数据安全是我们的首要任务</Text>
        <Text className='tip-text'>📱 注册后即可享受个性化星座和命理分析</Text>
        <Text className='tip-text'>✨ 免费用户可体验基础功能</Text>
      </View>
    </View>
  )
}

export default Register