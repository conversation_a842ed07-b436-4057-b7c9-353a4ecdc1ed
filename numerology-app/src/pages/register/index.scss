.register-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 40rpx;

  .header {
    text-align: center;
    margin-bottom: 60rpx;
    
    .title {
      font-size: 48rpx;
      font-weight: bold;
      color: white;
      margin-bottom: 16rpx;
    }
    
    .subtitle {
      font-size: 28rpx;
      color: rgba(255, 255, 255, 0.8);
    }
  }

  .form-container {
    background: white;
    border-radius: 24rpx;
    padding: 40rpx;
    margin-bottom: 40rpx;
    box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);

    .form-item {
      margin-bottom: 32rpx;

      .label {
        display: block;
        font-size: 28rpx;
        color: #333;
        margin-bottom: 12rpx;
        font-weight: 500;
      }

      .input {
        width: 100%;
        height: 80rpx;
        border: 2rpx solid #e0e0e0;
        border-radius: 12rpx;
        padding: 0 20rpx;
        font-size: 28rpx;
        background: #fafafa;
        
        &:focus {
          border-color: #667eea;
          background: white;
        }
      }
    }

    .register-btn {
      width: 100%;
      height: 88rpx;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border: none;
      border-radius: 12rpx;
      color: white;
      font-size: 32rpx;
      font-weight: bold;
      margin-top: 20rpx;
      
      &[loading] {
        opacity: 0.7;
      }
    }

    .login-link {
      text-align: center;
      margin-top: 24rpx;
      
      .link-text {
        color: #667eea;
        font-size: 26rpx;
        text-decoration: underline;
      }
    }
  }

  .tips {
    .tip-text {
      display: block;
      color: rgba(255, 255, 255, 0.9);
      font-size: 24rpx;
      text-align: center;
      margin-bottom: 12rpx;
      line-height: 1.6;
    }
  }
}