import Taro from '@tarojs/taro'

interface StorageItem {
  value: any
  expiry?: number
}

/**
 * 跨平台存储工具类
 */
export class Storage {
  /**
   * 设置存储项
   * @param key 键名
   * @param value 值
   * @param expiry 过期时间（毫秒）
   */
  static async setItem(key: string, value: any, expiry?: number): Promise<void> {
    try {
      const item: StorageItem = {
        value,
        expiry: expiry ? Date.now() + expiry : undefined
      }
      
      if (process.env.TARO_ENV === 'h5') {
        localStorage.setItem(key, JSON.stringify(item))
      } else {
        await Taro.setStorage({
          key,
          data: item
        })
      }
    } catch (error) {
      console.error('Storage setItem error:', error)
    }
  }

  /**
   * 获取存储项
   * @param key 键名
   * @param defaultValue 默认值
   */
  static async getItem<T = any>(key: string, defaultValue?: T): Promise<T | undefined> {
    try {
      let item: StorageItem | null = null
      
      if (process.env.TARO_ENV === 'h5') {
        const stored = localStorage.getItem(key)
        item = stored ? JSON.parse(stored) : null
      } else {
        const result = await Taro.getStorage({ key })
        item = result.data
      }

      if (!item) {
        return defaultValue
      }

      // 检查是否过期
      if (item.expiry && Date.now() > item.expiry) {
        await this.removeItem(key)
        return defaultValue
      }

      return item.value
    } catch (error) {
      console.error('Storage getItem error:', error)
      return defaultValue
    }
  }

  /**
   * 移除存储项
   * @param key 键名
   */
  static async removeItem(key: string): Promise<void> {
    try {
      if (process.env.TARO_ENV === 'h5') {
        localStorage.removeItem(key)
      } else {
        await Taro.removeStorage({ key })
      }
    } catch (error) {
      console.error('Storage removeItem error:', error)
    }
  }

  /**
   * 清空所有存储
   */
  static async clear(): Promise<void> {
    try {
      if (process.env.TARO_ENV === 'h5') {
        localStorage.clear()
      } else {
        await Taro.clearStorage()
      }
    } catch (error) {
      console.error('Storage clear error:', error)
    }
  }

  /**
   * 获取存储信息
   */
  static async getStorageInfo(): Promise<any> {
    try {
      if (process.env.TARO_ENV === 'h5') {
        return {
          keys: Object.keys(localStorage),
          currentSize: JSON.stringify(localStorage).length,
          limitSize: 5 * 1024 * 1024 // 5MB 估算值
        }
      } else {
        return await Taro.getStorageInfo()
      }
    } catch (error) {
      console.error('Storage getStorageInfo error:', error)
      return {}
    }
  }
}

/**
 * 缓存管理器
 */
export class CacheManager {
  private static readonly CACHE_PREFIX = 'cache_'
  
  /**
   * 设置缓存
   * @param key 缓存键
   * @param data 缓存数据
   * @param ttl 生存时间（毫秒，默认1小时）
   */
  static async set(key: string, data: any, ttl: number = 60 * 60 * 1000): Promise<void> {
    const cacheKey = this.CACHE_PREFIX + key
    await Storage.setItem(cacheKey, data, ttl)
  }

  /**
   * 获取缓存
   * @param key 缓存键
   */
  static async get<T = any>(key: string): Promise<T | null> {
    const cacheKey = this.CACHE_PREFIX + key
    return await Storage.getItem<T>(cacheKey, null)
  }

  /**
   * 移除缓存
   * @param key 缓存键
   */
  static async remove(key: string): Promise<void> {
    const cacheKey = this.CACHE_PREFIX + key
    await Storage.removeItem(cacheKey)
  }

  /**
   * 清空所有缓存
   */
  static async clearAll(): Promise<void> {
    try {
      const info = await Storage.getStorageInfo()
      const cacheKeys = info.keys?.filter((key: string) => key.startsWith(this.CACHE_PREFIX)) || []
      
      for (const key of cacheKeys) {
        await Storage.removeItem(key)
      }
    } catch (error) {
      console.error('Clear cache error:', error)
    }
  }
}

/**
 * 用户数据持久化
 */
export class UserPersistence {
  private static readonly USER_KEY = 'user_data'
  private static readonly TOKEN_KEY = 'access_token'
  private static readonly REFRESH_TOKEN_KEY = 'refresh_token'

  /**
   * 保存用户信息
   */
  static async saveUser(userData: any): Promise<void> {
    await Storage.setItem(this.USER_KEY, userData)
  }

  /**
   * 获取用户信息
   */
  static async getUser(): Promise<any> {
    return await Storage.getItem(this.USER_KEY)
  }

  /**
   * 保存token
   */
  static async saveTokens(accessToken: string, refreshToken?: string): Promise<void> {
    await Storage.setItem(this.TOKEN_KEY, accessToken)
    if (refreshToken) {
      await Storage.setItem(this.REFRESH_TOKEN_KEY, refreshToken)
    }
  }

  /**
   * 获取访问token
   */
  static async getAccessToken(): Promise<string | null> {
    return await Storage.getItem(this.TOKEN_KEY)
  }

  /**
   * 获取刷新token
   */
  static async getRefreshToken(): Promise<string | null> {
    return await Storage.getItem(this.REFRESH_TOKEN_KEY)
  }

  /**
   * 清除用户数据
   */
  static async clearUserData(): Promise<void> {
    await Storage.removeItem(this.USER_KEY)
    await Storage.removeItem(this.TOKEN_KEY)
    await Storage.removeItem(this.REFRESH_TOKEN_KEY)
  }
}

/**
 * 离线数据管理
 */
export class OfflineManager {
  private static readonly OFFLINE_QUEUE_KEY = 'offline_queue'
  
  /**
   * 添加离线请求到队列
   */
  static async addToQueue(request: {
    url: string
    method: string
    data?: any
    timestamp: number
  }): Promise<void> {
    const queue = await Storage.getItem(this.OFFLINE_QUEUE_KEY, [])
    queue.push(request)
    await Storage.setItem(this.OFFLINE_QUEUE_KEY, queue)
  }

  /**
   * 获取离线队列
   */
  static async getQueue(): Promise<any[]> {
    return await Storage.getItem(this.OFFLINE_QUEUE_KEY, [])
  }

  /**
   * 清空离线队列
   */
  static async clearQueue(): Promise<void> {
    await Storage.removeItem(this.OFFLINE_QUEUE_KEY)
  }

  /**
   * 移除队列中的指定项
   */
  static async removeFromQueue(index: number): Promise<void> {
    const queue = await this.getQueue()
    queue.splice(index, 1)
    await Storage.setItem(this.OFFLINE_QUEUE_KEY, queue)
  }
}